import { useState } from 'react'
import { Award, Users, Star } from 'lucide-react'
import GiveRecognitionModal from './GiveRecognitionModal'
import './RecognitionDemo.css'

const RecognitionDemo = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const recentRecognitions = [
    {
      id: 1,
      sender: '<PERSON>',
      recipient: '<PERSON>',
      category: 'Great Teamwork',
      points: 25,
      message: 'Amazing collaboration on the new feature!',
      time: '2 hours ago'
    },
    {
      id: 2,
      sender: '<PERSON>',
      recipient: '<PERSON>',
      category: 'Innovation',
      points: 30,
      message: 'Brilliant solution to the performance issue!',
      time: '5 hours ago'
    },
    {
      id: 3,
      sender: '<PERSON>',
      recipient: '<PERSON>',
      category: 'Leadership',
      points: 35,
      message: 'Outstanding leadership during the project crunch!',
      time: '1 day ago'
    }
  ]

  return (
    <div className="recognition-demo">
      <div className="demo-header">
        <h1>🏆 Employee Recognition System</h1>
        <p>Appreciate and celebrate your colleagues' amazing work</p>
      </div>

      <div className="demo-stats">
        <div className="stat-card">
          <Award className="stat-icon" />
          <div className="stat-content">
            <h3>156</h3>
            <p>Total Recognitions</p>
          </div>
        </div>
        <div className="stat-card">
          <Users className="stat-icon" />
          <div className="stat-content">
            <h3>42</h3>
            <p>Active Users</p>
          </div>
        </div>
        <div className="stat-card">
          <Star className="stat-icon" />
          <div className="stat-content">
            <h3>2,340</h3>
            <p>Points Awarded</p>
          </div>
        </div>
      </div>

      <div className="demo-actions">
        <button 
          className="give-recognition-btn"
          onClick={() => setIsModalOpen(true)}
        >
          <Award className="btn-icon" />
          Give Recognition
        </button>
      </div>

      <div className="recent-recognitions">
        <h2>Recent Recognitions</h2>
        <div className="recognition-list">
          {recentRecognitions.map(recognition => (
            <div key={recognition.id} className="recognition-card">
              <div className="recognition-header">
                <div className="recognition-info">
                  <span className="sender">{recognition.sender}</span>
                  <span className="arrow">→</span>
                  <span className="recipient">{recognition.recipient}</span>
                </div>
                <div className="recognition-time">{recognition.time}</div>
              </div>
              <div className="recognition-content">
                <div className="recognition-category">{recognition.category}</div>
                <div className="recognition-message">{recognition.message}</div>
                <div className="recognition-points">
                  <Star className="points-icon" />
                  {recognition.points} points
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <GiveRecognitionModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  )
}

export default RecognitionDemo
