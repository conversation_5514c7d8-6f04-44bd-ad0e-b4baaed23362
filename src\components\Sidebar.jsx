import { useState } from 'react'
import {
  Home,
  Users,
  Award,
  Users2,
  BarChart3,
  Calendar,
  Image,
  Trophy,
  Lightbulb,
  BookOpen,
  Settings,
  HelpCircle,
  LogOut,
  ChevronRight,
  Sparkles,
  Star,
  Zap,
  Heart,
  Target
} from 'lucide-react'
import './Sidebar.css'

const Sidebar = () => {
  const [activeItem, setActiveItem] = useState('Recognition')
  const [isCollapsed, setIsCollapsed] = useState(false)

  const menuItems = [
    {
      icon: Home,
      label: 'News Feed',
      color: '#FF6B9D',
      gradient: 'linear-gradient(135deg, #FF6B9D, #C44569)',
      description: 'Latest updates and posts'
    },
    {
      icon: Users,
      label: 'Profiles',
      color: '#4ECDC4',
      gradient: 'linear-gradient(135deg, #4ECDC4, #44A08D)',
      description: 'Team member profiles'
    },
    {
      icon: Award,
      label: 'Recognition',
      color: '#45B7D1',
      gradient: 'linear-gradient(135deg, #45B7D1, #2980B9)',
      badge: '2',
      description: 'Give and receive recognition'
    },
    {
      icon: Users2,
      label: 'Groups',
      color: '#96CEB4',
      gradient: 'linear-gradient(135deg, #96CEB4, #6AB7AA)',
      description: 'Team groups and departments'
    },
    {
      icon: BarChart3,
      label: 'Polls',
      color: '#FFEAA7',
      gradient: 'linear-gradient(135deg, #FFEAA7, #FDCB6E)',
      description: 'Surveys and feedback'
    },
    {
      icon: Calendar,
      label: 'Events',
      color: '#DDA0DD',
      gradient: 'linear-gradient(135deg, #DDA0DD, #DA70D6)',
      badge: '1',
      description: 'Company events and meetings'
    },
    {
      icon: Image,
      label: 'Media',
      color: '#FFB6C1',
      gradient: 'linear-gradient(135deg, #FFB6C1, #FF91A4)',
      description: 'Photos and videos'
    },
    {
      icon: Trophy,
      label: 'Leaderboard',
      color: '#87CEEB',
      gradient: 'linear-gradient(135deg, #87CEEB, #5DADE2)',
      description: 'Top performers'
    },
    {
      icon: Lightbulb,
      label: 'Spotlights',
      color: '#F0E68C',
      gradient: 'linear-gradient(135deg, #F0E68C, #DAA520)',
      description: 'Featured content'
    },
    {
      icon: BookOpen,
      label: 'HR Blog',
      color: '#98FB98',
      gradient: 'linear-gradient(135deg, #98FB98, #90EE90)',
      description: 'HR news and articles'
    },
  ]

  const bottomMenuItems = [
    {
      icon: Settings,
      label: 'Settings',
      color: '#9CA3AF',
      gradient: 'linear-gradient(135deg, #9CA3AF, #6B7280)'
    },
    {
      icon: HelpCircle,
      label: 'Help & Support',
      color: '#8B5CF6',
      gradient: 'linear-gradient(135deg, #8B5CF6, #7C3AED)'
    },
  ]

  const handleItemClick = (label) => {
    setActiveItem(label)
  }

  return (
    <div className="sidebar">
      {/* Enhanced Header with Gradient */}
      <div className="sidebar-header">
        <div className="sidebar-logo">
          <div className="logo-icon">
            <Sparkles className="logo-sparkle" />
          </div>
          <div className="logo-text">
            <h1 className="sidebar-title">HR Social Hub</h1>
            <p className="sidebar-subtitle">Connect • Recognize • Grow</p>
          </div>
        </div>
        <div className="header-decoration">
          <div className="decoration-dot"></div>
          <div className="decoration-dot"></div>
          <div className="decoration-dot"></div>
        </div>
      </div>

      {/* Enhanced Navigation Menu */}
      <nav className="sidebar-nav">
        <div className="nav-section">
          <h3 className="nav-section-title">
            <Star className="section-icon" />
            Main Navigation
          </h3>
          <ul className="sidebar-menu">
            {menuItems.map((item, index) => {
              const isActive = activeItem === item.label
              return (
                <li key={index} className="menu-item-wrapper">
                  <a
                    href="#"
                    onClick={(e) => {
                      e.preventDefault()
                      handleItemClick(item.label)
                    }}
                    className={`sidebar-item ${isActive ? 'active' : ''}`}
                    style={{
                      '--item-color': item.color,
                      '--item-gradient': item.gradient
                    }}
                  >
                    <div className="item-icon-wrapper">
                      <item.icon className="sidebar-item-icon" />
                      <div className="icon-glow"></div>
                    </div>
                    <div className="item-content">
                      <span className="sidebar-item-label">{item.label}</span>
                      {item.description && (
                        <span className="sidebar-item-description">{item.description}</span>
                      )}
                    </div>
                    {item.badge && (
                      <span className="sidebar-badge" style={{ backgroundColor: item.color }}>
                        {item.badge}
                      </span>
                    )}
                    <ChevronRight className="item-arrow" />
                  </a>
                  {isActive && <div className="active-indicator"></div>}
                </li>
              )
            })}
          </ul>
        </div>

        {/* Bottom Menu Section */}
        <div className="nav-section bottom-section">
          <h3 className="nav-section-title">
            <Settings className="section-icon" />
            Quick Access
          </h3>
          <ul className="sidebar-menu">
            {bottomMenuItems.map((item, index) => {
              const isActive = activeItem === item.label
              return (
                <li key={index} className="menu-item-wrapper">
                  <a
                    href="#"
                    onClick={(e) => {
                      e.preventDefault()
                      handleItemClick(item.label)
                    }}
                    className={`sidebar-item ${isActive ? 'active' : ''}`}
                    style={{
                      '--item-color': item.color,
                      '--item-gradient': item.gradient
                    }}
                  >
                    <div className="item-icon-wrapper">
                      <item.icon className="sidebar-item-icon" />
                      <div className="icon-glow"></div>
                    </div>
                    <div className="item-content">
                      <span className="sidebar-item-label">{item.label}</span>
                    </div>
                    <ChevronRight className="item-arrow" />
                  </a>
                  {isActive && <div className="active-indicator"></div>}
                </li>
              )
            })}
          </ul>
        </div>
      </nav>

      {/* Enhanced Profile Section */}
      <div className="sidebar-profile">
        <div className="profile-background">
          <div className="profile-pattern"></div>
        </div>
        <div className="sidebar-profile-content">
          <div className="sidebar-avatar">
            <span className="sidebar-avatar-text">JD</span>
            <div className="avatar-status"></div>
            <div className="avatar-ring"></div>
          </div>
          <div className="sidebar-profile-info">
            <p className="sidebar-profile-name">John Doe</p>
            <p className="sidebar-profile-title">Product Designer</p>
            <div className="profile-stats">
              <div className="stat-item">
                <Heart className="stat-icon" />
                <span>125 pts</span>
              </div>
              <div className="stat-item">
                <Target className="stat-icon" />
                <span>Level 5</span>
              </div>
            </div>
          </div>
        </div>
        <button className="logout-btn">
          <LogOut className="logout-icon" />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  )
}

export default Sidebar
