/* Recipient Details Modal */
.recipient-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.recipient-modal-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

/* Header */
.recipient-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.recipient-modal-profile {
  display: flex;
  align-items: center;
  gap: 16px;
}

.recipient-modal-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 24px;
  color: white;
}

.recipient-modal-avatar.blue {
  background-color: #3b82f6;
}

.recipient-modal-avatar.gray {
  background-color: #6b7280;
}

.recipient-modal-avatar.light-gray {
  background-color: #9ca3af;
}

.recipient-modal-info h2 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.recipient-modal-title {
  font-size: 16px;
  color: #3b82f6;
  font-weight: 500;
  margin: 0 0 2px 0;
}

.recipient-modal-department {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.recipient-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.recipient-modal-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.close-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

/* Stats Overview */
.recipient-modal-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 24px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Content */
.recipient-modal-content {
  padding: 24px;
}

.modal-section {
  margin-bottom: 32px;
}

.modal-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
}

.section-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
}

/* Achievements Grid */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.achievement-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.achievement-card-icon {
  width: 24px;
  height: 24px;
  color: #3b82f6;
  flex-shrink: 0;
}

.achievement-card-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 2px 0;
}

.achievement-card-desc {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

/* Recognitions List */
.recognitions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recognition-card {
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.recognition-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.recognition-from {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recognition-avatar {
  width: 32px;
  height: 32px;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.recognition-author {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.recognition-category {
  font-size: 12px;
  color: #3b82f6;
  background-color: #eff6ff;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.recognition-date {
  font-size: 12px;
  color: #6b7280;
}

.recognition-message {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin: 8px 0;
}

.recognition-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.recognition-likes {
  font-size: 12px;
  color: #6b7280;
}

/* Trend Chart */
.trend-chart {
  display: flex;
  align-items: end;
  gap: 12px;
  height: 120px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.trend-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
  position: relative;
}

.trend-bar-fill {
  background: linear-gradient(to top, #3b82f6, #60a5fa);
  width: 100%;
  border-radius: 4px 4px 0 0;
  min-height: 4px;
  margin-bottom: auto;
}

.trend-month {
  font-size: 12px;
  color: #6b7280;
  margin-top: 8px;
}

.trend-value {
  font-size: 10px;
  color: #374151;
  font-weight: 500;
  position: absolute;
  top: -20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .recipient-modal-overlay {
    padding: 10px;
  }
  
  .recipient-modal-container {
    max-height: 95vh;
  }
  
  .recipient-modal-header {
    padding: 16px;
  }
  
  .recipient-modal-profile {
    gap: 12px;
  }
  
  .recipient-modal-avatar {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }
  
  .recipient-modal-stats {
    grid-template-columns: repeat(2, 1fr);
    padding: 16px;
  }
  
  .recipient-modal-content {
    padding: 16px;
  }
  
  .achievements-grid {
    grid-template-columns: 1fr;
  }
  
  .trend-chart {
    height: 100px;
  }
}
