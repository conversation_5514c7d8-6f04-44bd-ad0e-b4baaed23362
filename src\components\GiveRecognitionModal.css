/* Minimal Colorful Give Recognition Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: white;
  border-radius: 24px;
  padding: 32px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
}

.modal-header h2 {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.header-icon {
  width: 32px;
  height: 32px;
  color: #FF6B9D;
}

.close-button {
  background: #f3f4f6;
  border: none;
  border-radius: 12px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: #e5e7eb;
  transform: scale(1.1);
}

.modal-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group label {
  font-size: 18px;
  font-weight: 700;
  color: #374151;
}

/* Person Selection */
.person-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.person-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.person-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.person-emoji {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

/* Category Selection */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}

.category-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 24px;
  height: 24px;
}

/* Quick Messages */
.quick-messages {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-msg-btn {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #374151;
}

.quick-msg-btn:hover {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  border-color: #4ECDC4;
  color: white;
  transform: translateY(-1px);
}

/* Message Input */
.message-input {
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  font-size: 16px;
  resize: vertical;
  transition: all 0.3s ease;
}

.message-input:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

/* Points Section */
.points-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.points-slider {
  flex: 1;
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  outline: none;
  cursor: pointer;
}

.points-display {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 700;
}

.points-star {
  width: 20px;
  height: 20px;
}

/* Submit Button */
.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 32px;
  border: none;
  border-radius: 16px;
  font-size: 18px;
  font-weight: 700;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
  text-align: center;
  padding: 48px 32px;
}

.success-animation {
  margin-bottom: 24px;
}

.success-circle {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  animation: bounce 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.success-icon {
  width: 40px;
  height: 40px;
  color: white;
}

.success-message h3 {
  font-size: 28px;
  font-weight: 800;
  color: #374151;
  margin: 0 0 8px 0;
}

.success-message p {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 24px 0;
}

.success-details {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.success-details span {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  color: white;
}

/* Responsive */
@media (max-width: 640px) {
  .modal-container {
    padding: 24px;
    margin: 16px;
  }
  
  .person-grid,
  .category-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-messages {
    flex-direction: column;
  }
  
  .points-section {
    flex-direction: column;
    align-items: stretch;
  }
}
