/* Enhanced Colorful Give Recognition Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container {
  background: white;
  border-radius: 28px;
  padding: 0;
  max-width: 800px;
  width: 100%;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
  animation: slideUp 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

@keyframes slideUp {
  from { transform: translateY(50px) scale(0.95); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}

/* Enhanced Header */
.modal-header {
  padding: 32px 40px 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.9;
  z-index: 1;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 2;
}

.header-icon-wrapper {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.header-icon {
  width: 32px;
  height: 32px;
  color: white;
}

.header-text {
  flex: 1;
}

.modal-title {
  font-size: 32px;
  font-weight: 900;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  font-weight: 500;
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 3;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.close-icon {
  width: 24px;
  height: 24px;
  color: white;
}

/* Progress Steps */
.progress-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  padding: 24px 40px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 100%;
  width: 24px;
  height: 2px;
  background: #e5e7eb;
  transition: background-color 0.3s ease;
}

.step.completed:not(:last-child)::after {
  background: var(--primary-color, #FF6B9D);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.step-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-align: center;
  transition: color 0.3s ease;
}

.step.active .step-label,
.step.completed .step-label {
  color: #374151;
}

/* Theme Selector */
.theme-selector {
  padding: 20px 40px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.theme-selector h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.theme-options {
  display: flex;
  gap: 12px;
}

.theme-option {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-option:hover {
  transform: scale(1.1);
}

.theme-option.selected {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-preview {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8px;
}

/* Form Styles */
.modal-form {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 40px 40px;
}

.form-step {
  animation: stepSlideIn 0.4s ease-out;
}

@keyframes stepSlideIn {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

.step-header {
  text-align: center;
  margin-bottom: 32px;
}

.step-header h3 {
  font-size: 28px;
  font-weight: 800;
  margin: 0 0 8px 0;
}

.step-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* Step 1: Recipients */
.department-filter {
  margin-bottom: 20px;
}

.department-filter label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.department-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.department-select:focus {
  outline: none;
  border-color: #4ECDC4;
}

.search-container {
  position: relative;
  margin-bottom: 24px;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.recipients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.recipient-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.recipient-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.recipient-card.selected {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.recipient-avatar {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.recipient-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.recipient-name {
  font-size: 16px;
  font-weight: 700;
  color: #374151;
}

.recipient-role {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.recipient-department {
  font-size: 12px;
  font-weight: 600;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.recipient-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online {
  background: #10b981;
}

.status-indicator.away {
  background: #f59e0b;
}

.status-indicator.offline {
  background: #6b7280;
}

.recipient-status span {
  font-size: 12px;
  color: #6b7280;
}

.points {
  font-size: 12px;
  font-weight: 600;
  color: #4ECDC4;
}

.selected-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
}

.selected-summary {
  background: #f8fafc;
  border-radius: 16px;
  padding: 20px;
  border: 2px solid #e5e7eb;
}

.selected-summary h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 700;
  color: #374151;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.remove-tag {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  color: white;
  transition: background-color 0.3s ease;
}

.remove-tag:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Step 2: Categories */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.category-card {
  display: flex;
  flex-direction: column;
  padding: 24px;
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-card.selected::before {
  opacity: 1;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.category-icon {
  width: 32px;
  height: 32px;
}

.category-badge {
  font-size: 24px;
}

.category-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 8px 0;
  position: relative;
  z-index: 1;
}

.category-description {
  font-size: 14px;
  margin: 0 0 16px 0;
  opacity: 0.8;
  position: relative;
  z-index: 1;
}

.category-points {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  position: relative;
  z-index: 1;
  align-self: flex-start;
}

.points-icon {
  width: 16px;
  height: 16px;
}

.selected-check {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 24px;
  height: 24px;
  color: white;
  z-index: 2;
}

.category-preview {
  margin-top: 24px;
  border-radius: 16px;
  padding: 24px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.preview-content {
  text-align: center;
}

.preview-badge {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.preview-content h4 {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.preview-content p {
  font-size: 14px;
  margin: 0 0 16px 0;
  opacity: 0.9;
}

.preview-points {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

/* Quick Messages */
.quick-messages {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-msg-btn {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #374151;
}

.quick-msg-btn:hover {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  border-color: #4ECDC4;
  color: white;
  transform: translateY(-1px);
}

/* Message Input */
.message-input {
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  font-size: 16px;
  resize: vertical;
  transition: all 0.3s ease;
}

.message-input:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

/* Points Section */
.points-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.points-slider {
  flex: 1;
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  outline: none;
  cursor: pointer;
}

.points-display {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 700;
}

.points-star {
  width: 20px;
  height: 20px;
}

/* Submit Button */
.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 32px;
  border: none;
  border-radius: 16px;
  font-size: 18px;
  font-weight: 700;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
  text-align: center;
  padding: 48px 32px;
}

.success-animation {
  margin-bottom: 24px;
}

.success-circle {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  animation: bounce 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.success-icon {
  width: 40px;
  height: 40px;
  color: white;
}

.success-message h3 {
  font-size: 28px;
  font-weight: 800;
  color: #374151;
  margin: 0 0 8px 0;
}

.success-message p {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 24px 0;
}

/* Enhanced Step-by-Step Styles */
.message-templates {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.template-button {
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  background: white;
  font-size: 14px;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1.5;
}

.template-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #4ECDC4;
}

.message-input-container {
  position: relative;
  margin-bottom: 24px;
}

.message-textarea {
  width: 100%;
  padding: 20px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  min-height: 140px;
  transition: all 0.3s ease;
}

.message-textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.message-tools {
  display: flex;
  gap: 16px;
  margin-top: 12px;
}

.tool-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.visibility-section {
  margin-bottom: 24px;
}

.visibility-section h4 {
  font-size: 16px;
  font-weight: 700;
  color: #374151;
  margin: 0 0 16px 0;
}

.visibility-option {
  cursor: pointer;
}

.visibility-option input[type="radio"] {
  display: none;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  background: white;
  transition: all 0.3s ease;
}

.option-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-icon {
  font-size: 20px;
}

.option-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-label {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.option-desc {
  font-size: 14px;
  color: #6b7280;
}

/* Navigation Styles */
.form-navigation {
  padding: 24px 40px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.nav-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: 2px solid;
  border-radius: 12px;
  background: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.prev-button {
  background: white;
}

.next-button {
  color: white;
  border: none;
}

.cancel-button {
  padding: 12px 24px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  font-size: 16px;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-button:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.step-progress {
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

/* Enhanced Success Screen */
.success-screen {
  padding: 60px 40px;
  text-align: center;
  position: relative;
}

.confetti-animation {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 200px;
  pointer-events: none;
}

.confetti-piece {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 2px;
  animation: confettiFall 2s ease-out forwards;
}

@keyframes confettiFall {
  0% {
    transform: translateY(-20px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(200px) rotate(360deg);
    opacity: 0;
  }
}

.success-title {
  font-size: 32px;
  font-weight: 900;
  margin: 0 0 16px 0;
}

.success-details {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.success-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 16px;
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.stat-icon {
  width: 20px;
  height: 20px;
}

.success-details {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.success-details span {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  color: white;
}

/* Responsive */
@media (max-width: 640px) {
  .modal-container {
    padding: 24px;
    margin: 16px;
  }
  
  .person-grid,
  .category-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-messages {
    flex-direction: column;
  }
  
  .points-section {
    flex-direction: column;
    align-items: stretch;
  }
}
