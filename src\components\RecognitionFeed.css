/* Enhanced Colorful RecognitionFeed Component Styles */
.recognition-feed-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

.recognition-feed-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.recognition-feed-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 20px;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(78, 205, 196, 0.05));
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.recognition-feed-title {
  font-size: 24px;
  font-weight: 800;
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recognition-count {
  font-size: 14px;
  color: #6b7280;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.8);
  padding: 4px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #f8fafc, #ffffff);
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-toggle-btn:hover {
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  border-color: #FF6B9D;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
}

.filter-toggle-btn.active {
  background: linear-gradient(135deg, #45B7D1, #2980B9);
  border-color: #45B7D1;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(69, 183, 209, 0.4);
}

.filter-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.filter-toggle-btn:hover .filter-icon,
.filter-toggle-btn.active .filter-icon {
  transform: rotate(180deg);
}

.filter-count {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  font-size: 11px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Filter Panel */
.filter-panel {
  padding: 16px 24px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.filter-row {
  display: flex;
  align-items: end;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 150px;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-label-icon {
  width: 12px;
  height: 12px;
  color: #6b7280;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-filters-btn {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  height: fit-content;
}

.clear-filters-btn:hover {
  background-color: #dc2626;
}

.recognition-feed-content {
  padding: 0 16px 16px;
}

.recognition-item {
  padding: 16px;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.recognition-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--category-gradient, linear-gradient(135deg, #FF6B9D, #4ECDC4));
  transition: width 0.3s ease;
}

.recognition-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.recognition-item:hover::before {
  width: 8px;
}

.recognition-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 14px;
}

.recognition-avatars {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recognition-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  transition: transform 0.3s ease;
}

.recognition-avatar:hover {
  transform: scale(1.1);
}

.recognition-avatar-text {
  color: white;
  font-weight: 700;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #f8fafc, #ffffff);
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.recognition-arrow {
  width: 12px;
  height: 12px;
  color: #6b7280;
}

.recognition-info {
  flex: 1;
}

.recognition-names {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.recognition-from,
.recognition-to {
  font-size: 14px;
  font-weight: 700;
  color: #111827;
}

.recognition-arrow-text {
  color: #9ca3af;
  font-weight: 400;
  font-size: 12px;
}

.recognition-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.category-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); }
  to { box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3); }
}

.category-icon {
  width: 14px;
  height: 14px;
}

.category-label {
  text-transform: capitalize;
}

.category-emoji {
  font-size: 14px;
}

.trending-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-radius: 12px;
  color: white;
  font-size: 10px;
  font-weight: 600;
  box-shadow: 0 1px 4px rgba(245, 158, 11, 0.3);
  animation: trending 1.5s ease-in-out infinite;
}

@keyframes trending {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.trending-icon {
  width: 10px;
  height: 10px;
}

.recognition-timestamp-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6px;
}

.recognition-timestamp {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.points-badge {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 10px;
  font-weight: 700;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}

.points-icon {
  width: 10px;
  height: 10px;
}

.recognition-content {
  margin-bottom: 14px;
}

.recognition-message {
  font-size: 14px;
  color: #374151;
  margin: 0;
  line-height: 1.5;
  padding: 12px 14px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  border-left: 3px solid #e5e7eb;
  position: relative;
}

.recognition-message::before {
  content: '"';
  position: absolute;
  top: -6px;
  left: 12px;
  font-size: 24px;
  color: #e5e7eb;
  font-family: serif;
}

.recognition-message::after {
  content: '"';
  position: absolute;
  bottom: -16px;
  right: 12px;
  font-size: 24px;
  color: #e5e7eb;
  font-family: serif;
}

.recognition-from {
  margin: 0 8px;
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.recognition-arrow {
  width: 16px;
  height: 16px;
  color: #9ca3af;
  margin: 0 4px;
}

.recognition-to {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.recognition-timestamp {
  margin-left: auto;
  font-size: 12px;
  color: #6b7280;
}

.recognition-message {
  font-size: 14px;
  color: #374151;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.recognition-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.recognition-action {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #f8fafc, #ffffff);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.recognition-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.recognition-action:hover.like-action {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: #ef4444;
  color: white;
}

.recognition-action:hover.comment-action {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-color: #3b82f6;
  color: white;
}

.recognition-action:hover.share-action {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: #10b981;
  color: white;
}

.recognition-action.liked {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: #ef4444;
  color: white;
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.recognition-action-icon {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease;
}

.recognition-action:hover .recognition-action-icon {
  transform: scale(1.1);
}

.recognition-action-icon.filled {
  fill: currentColor;
}

/* Comments Section */
.comments-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.comments-list {
  margin-bottom: 12px;
}

.comment-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.comment-avatar {
  width: 24px;
  height: 24px;
  background-color: #6b7280;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  flex-shrink: 0;
}

.comment-avatar span {
  color: white;
  font-size: 10px;
  font-weight: 600;
}

.comment-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.comment-author {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.comment-text {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.add-comment {
  margin-top: 12px;
}

.comment-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.comment-input:focus {
  border-color: #3b82f6;
}

.comment-send-btn {
  background-color: #3b82f6;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.comment-send-btn:hover:not(:disabled) {
  background-color: #2563eb;
}

.comment-send-btn:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
}

.send-icon {
  width: 14px;
  height: 14px;
  color: white;
}

/* Recognition Attachments */
.recognition-attachments {
  margin: 12px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attachment-preview {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.attachment-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  display: block;
}

.attachment-file {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f9fafb;
  gap: 8px;
}

.attachment-file-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.attachment-file-name {
  font-size: 12px;
  color: #374151;
}

/* Comment Upload Functionality */
.comment-input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.upload-btn {
  background-color: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.upload-btn:hover {
  background-color: #e5e7eb;
}

.upload-icon {
  width: 14px;
  height: 14px;
  color: #6b7280;
}

/* Comment Attachments Preview */
.comment-attachments-preview {
  margin: 8px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.comment-attachment-item {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.comment-attachment-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  display: block;
}

.comment-attachment-file {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background-color: #f9fafb;
  gap: 6px;
  min-width: 60px;
}

.comment-attachment-icon {
  width: 12px;
  height: 12px;
  color: #6b7280;
}

.comment-attachment-name {
  font-size: 10px;
  color: #374151;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-attachment-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
}

.remove-attachment-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

/* Comment Attachments Display */
.comment-attachments {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.comment-attachment-display {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.comment-attachment-display-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.comment-attachment-display-file {
  display: flex;
  align-items: center;
  padding: 4px 6px;
  background-color: #f9fafb;
  gap: 4px;
}

.comment-attachment-display-icon {
  width: 12px;
  height: 12px;
  color: #6b7280;
}

.comment-attachment-display-file span {
  font-size: 10px;
  color: #374151;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive Design for Filters */
@media (max-width: 768px) {
  .recognition-feed-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }

  .header-left {
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-panel {
    padding: 12px 16px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-group {
    min-width: auto;
    width: 100%;
  }

  .clear-filters-btn {
    width: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .recognition-item {
    padding: 12px;
  }

  .recognition-feed-header {
    padding: 12px;
  }

  .recognition-header {
    flex-wrap: wrap;
    gap: 4px;
  }

  .recognition-timestamp {
    margin-left: 0;
    width: 100%;
    margin-top: 4px;
  }
}
