import { useState, useEffect } from 'react'
import {
  Calendar, TrendingUp, Award, Star, Users, Target, Zap, Heart,
  Trophy, Gift, Clock, BarChart3, PieChart, Activity, ArrowUp,
  ArrowDown, Minus, ChevronRight, Filter, Download, RefreshCw,
  Eye, Share2, Bookmark, MessageCircle, ThumbsUp, Sparkles
} from 'lucide-react'
import './MonthlyStats.css'

const MonthlyStats = () => {
  const [selectedMonth, setSelectedMonth] = useState('current')
  const [showDetails, setShowDetails] = useState(false)
  const [animateStats, setAnimateStats] = useState(false)

  useEffect(() => {
    setAnimateStats(true)
    const timer = setTimeout(() => setAnimateStats(false), 1000)
    return () => clearTimeout(timer)
  }, [selectedMonth])

  const monthlyData = {
    current: {
      month: 'December 2024',
      period: 'Current Month',
      stats: [
        {
          id: 'sent',
          label: 'Recognition Sent',
          value: 24,
          maxValue: 50,
          previousValue: 18,
          color: '#FF6B9D',
          gradient: 'linear-gradient(135deg, #FF6B9D, #C44569)',
          icon: Gift,
          trend: 'up',
          trendValue: '+33%',
          details: {
            thisWeek: 8,
            lastWeek: 6,
            categories: [
              { name: 'Leadership', count: 8, color: '#FF6B9D' },
              { name: 'Teamwork', count: 7, color: '#4ECDC4' },
              { name: 'Innovation', count: 5, color: '#45B7D1' },
              { name: 'Excellence', count: 4, color: '#96CEB4' }
            ],
            topRecipients: [
              { name: 'Sarah Chen', count: 4, avatar: 'SC' },
              { name: 'Mike Torres', count: 3, avatar: 'MT' },
              { name: 'Lisa Wang', count: 3, avatar: 'LW' }
            ]
          }
        },
        {
          id: 'received',
          label: 'Recognition Received',
          value: 18,
          maxValue: 50,
          previousValue: 22,
          color: '#4ECDC4',
          gradient: 'linear-gradient(135deg, #4ECDC4, #44A08D)',
          icon: Heart,
          trend: 'down',
          trendValue: '-18%',
          details: {
            thisWeek: 5,
            lastWeek: 7,
            categories: [
              { name: 'Innovation', count: 6, color: '#4ECDC4' },
              { name: 'Leadership', count: 5, color: '#FF6B9D' },
              { name: 'Teamwork', count: 4, color: '#96CEB4' },
              { name: 'Excellence', count: 3, color: '#45B7D1' }
            ],
            fromColleagues: [
              { name: 'David Kim', count: 3, avatar: 'DK' },
              { name: 'Emily Johnson', count: 3, avatar: 'EJ' },
              { name: 'Alex Rodriguez', count: 2, avatar: 'AR' }
            ]
          }
        },
        {
          id: 'points',
          label: 'Points Earned',
          value: 1250,
          maxValue: 2000,
          previousValue: 980,
          color: '#45B7D1',
          gradient: 'linear-gradient(135deg, #45B7D1, #2980B9)',
          icon: Star,
          trend: 'up',
          trendValue: '+28%',
          details: {
            thisWeek: 320,
            lastWeek: 280,
            breakdown: [
              { source: 'Received Recognition', points: 900, color: '#45B7D1' },
              { source: 'Giving Recognition', points: 240, color: '#FF6B9D' },
              { source: 'Bonus Activities', points: 110, color: '#96CEB4' }
            ],
            ranking: { current: 5, previous: 8, total: 150 }
          }
        },
        {
          id: 'engagement',
          label: 'Team Engagement',
          value: 85,
          maxValue: 100,
          previousValue: 78,
          color: '#96CEB4',
          gradient: 'linear-gradient(135deg, #96CEB4, #6AB7AA)',
          icon: Users,
          trend: 'up',
          trendValue: '+9%',
          details: {
            thisWeek: 88,
            lastWeek: 82,
            activities: [
              { type: 'Comments', count: 15, color: '#96CEB4' },
              { type: 'Likes', count: 32, color: '#4ECDC4' },
              { type: 'Shares', count: 8, color: '#FF6B9D' },
              { type: 'Views', count: 124, color: '#45B7D1' }
            ],
            teamRank: 3
          }
        },
        {
          id: 'streak',
          label: 'Recognition Streak',
          value: 12,
          maxValue: 30,
          previousValue: 8,
          color: '#FFEAA7',
          gradient: 'linear-gradient(135deg, #FFEAA7, #FDCB6E)',
          icon: Zap,
          trend: 'up',
          trendValue: '+50%',
          details: {
            currentStreak: 12,
            longestStreak: 18,
            streakType: 'Daily Recognition',
            nextMilestone: 15,
            rewards: ['🔥 Fire Badge', '⚡ Lightning Badge']
          }
        },
        {
          id: 'impact',
          label: 'Recognition Impact',
          value: 92,
          maxValue: 100,
          previousValue: 87,
          color: '#DDA0DD',
          gradient: 'linear-gradient(135deg, #DDA0DD, #DA70D6)',
          icon: Target,
          trend: 'up',
          trendValue: '+6%',
          details: {
            reachScore: 92,
            influenceScore: 88,
            positivityScore: 95,
            teamMorale: '+15%',
            feedback: 'Your recognitions boost team morale significantly!'
          }
        }
      ],
      summary: {
        totalInteractions: 156,
        teamPosition: 5,
        departmentRank: 2,
        achievements: ['🏆 Top Recognizer', '⭐ Team Motivator', '💡 Innovation Catalyst'],
        nextGoals: [
          { goal: 'Send 30 recognitions', progress: 80, target: 30 },
          { goal: 'Reach 1500 points', progress: 83, target: 1500 },
          { goal: 'Maintain 15-day streak', progress: 80, target: 15 }
        ]
      }
    },
    previous: {
      month: 'November 2024',
      period: 'Previous Month',
      stats: [
        {
          id: 'sent',
          label: 'Recognition Sent',
          value: 18,
          maxValue: 50,
          previousValue: 15,
          color: '#FF6B9D',
          gradient: 'linear-gradient(135deg, #FF6B9D, #C44569)',
          icon: Gift,
          trend: 'up',
          trendValue: '+20%'
        },
        {
          id: 'received',
          label: 'Recognition Received',
          value: 22,
          maxValue: 50,
          previousValue: 19,
          color: '#4ECDC4',
          gradient: 'linear-gradient(135deg, #4ECDC4, #44A08D)',
          icon: Heart,
          trend: 'up',
          trendValue: '+16%'
        }
      ]
    }
  }

  const currentData = monthlyData[selectedMonth] || monthlyData.current

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return <ArrowUp className="trend-icon up" />
      case 'down': return <ArrowDown className="trend-icon down" />
      default: return <Minus className="trend-icon neutral" />
    }
  }

  return (
    <div className="monthly-stats-card">
      {/* Enhanced Header */}
      <div className="monthly-stats-header">
        <div className="header-content">
          <div className="header-title-section">
            <BarChart3 className="header-icon" />
            <div className="title-info">
              <h2 className="monthly-stats-title">Monthly Statistics</h2>
              <p className="monthly-stats-subtitle">{currentData.period}</p>
            </div>
            <div className="month-badge">
              <Calendar className="month-icon" />
              <span>{currentData.month}</span>
            </div>
          </div>
          <div className="header-actions">
            <button
              className="month-toggle-btn"
              onClick={() => setSelectedMonth(selectedMonth === 'current' ? 'previous' : 'current')}
            >
              <RefreshCw className="toggle-icon" />
              <span>{selectedMonth === 'current' ? 'Previous' : 'Current'}</span>
            </button>
            <button
              className="details-toggle-btn"
              onClick={() => setShowDetails(!showDetails)}
            >
              <Eye className="details-icon" />
              <span>{showDetails ? 'Hide' : 'Details'}</span>
            </button>
            <button className="export-btn">
              <Download className="export-icon" />
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Content */}
      <div className="monthly-stats-content">
        {currentData.stats.map((stat, index) => (
          <div
            key={stat.id}
            className={`stat-item ${animateStats ? 'animate-in' : ''}`}
            style={{
              animationDelay: `${index * 0.1}s`,
              '--stat-color': stat.color,
              '--stat-gradient': stat.gradient
            }}
          >
            {/* Enhanced Stat Header */}
            <div className="stat-header">
              <div className="stat-icon-wrapper" style={{ background: stat.gradient }}>
                <stat.icon className="stat-icon" />
              </div>
              <div className="stat-info">
                <div className="stat-main">
                  <span className="stat-label">{stat.label}</span>
                  <div className="stat-trend" style={{ color: stat.color }}>
                    {getTrendIcon(stat.trend)}
                    <span className="trend-value">{stat.trendValue}</span>
                  </div>
                </div>
                <div className="stat-value-container">
                  <span className="stat-value" style={{ color: stat.color }}>
                    {stat.value.toLocaleString()}
                  </span>
                  <span className="stat-unit">
                    {stat.id === 'points' ? 'pts' : stat.id === 'engagement' || stat.id === 'impact' ? '%' : ''}
                  </span>
                </div>
              </div>
            </div>

            {/* Enhanced Progress Bar */}
            <div className="stat-progress-container">
              <div
                className="stat-progress-bar"
                style={{
                  width: `${(stat.value / stat.maxValue) * 100}%`,
                  background: stat.gradient
                }}
              ></div>
              <div className="progress-labels">
                <span className="progress-current">{stat.value}</span>
                <span className="progress-max">/ {stat.maxValue}</span>
              </div>
            </div>

            {/* Detailed Information */}
            {showDetails && stat.details && (
              <div className="stat-details">
                {/* Week Comparison */}
                {stat.details.thisWeek && (
                  <div className="detail-section">
                    <h4 className="detail-title">Weekly Breakdown</h4>
                    <div className="week-comparison">
                      <div className="week-item">
                        <span className="week-label">This Week</span>
                        <span className="week-value" style={{ color: stat.color }}>
                          {stat.details.thisWeek}
                        </span>
                      </div>
                      <div className="week-item">
                        <span className="week-label">Last Week</span>
                        <span className="week-value">{stat.details.lastWeek}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Categories Breakdown */}
                {stat.details.categories && (
                  <div className="detail-section">
                    <h4 className="detail-title">Categories</h4>
                    <div className="categories-list">
                      {stat.details.categories.map((category, idx) => (
                        <div key={idx} className="category-item">
                          <div
                            className="category-dot"
                            style={{ backgroundColor: category.color }}
                          ></div>
                          <span className="category-name">{category.name}</span>
                          <span className="category-count">{category.count}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Top Recipients/Colleagues */}
                {(stat.details.topRecipients || stat.details.fromColleagues) && (
                  <div className="detail-section">
                    <h4 className="detail-title">
                      {stat.details.topRecipients ? 'Top Recipients' : 'From Colleagues'}
                    </h4>
                    <div className="people-list">
                      {(stat.details.topRecipients || stat.details.fromColleagues).map((person, idx) => (
                        <div key={idx} className="person-item">
                          <div
                            className="person-avatar"
                            style={{ backgroundColor: stat.color }}
                          >
                            {person.avatar}
                          </div>
                          <span className="person-name">{person.name}</span>
                          <span className="person-count">{person.count}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Points Breakdown */}
                {stat.details.breakdown && (
                  <div className="detail-section">
                    <h4 className="detail-title">Points Breakdown</h4>
                    <div className="breakdown-list">
                      {stat.details.breakdown.map((item, idx) => (
                        <div key={idx} className="breakdown-item">
                          <div
                            className="breakdown-dot"
                            style={{ backgroundColor: item.color }}
                          ></div>
                          <span className="breakdown-source">{item.source}</span>
                          <span className="breakdown-points">{item.points} pts</span>
                        </div>
                      ))}
                    </div>
                    {stat.details.ranking && (
                      <div className="ranking-info">
                        <Trophy className="ranking-icon" />
                        <span>Rank #{stat.details.ranking.current} of {stat.details.ranking.total}</span>
                        <span className="rank-change">
                          (↑{stat.details.ranking.previous - stat.details.ranking.current})
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* Activities */}
                {stat.details.activities && (
                  <div className="detail-section">
                    <h4 className="detail-title">Engagement Activities</h4>
                    <div className="activities-grid">
                      {stat.details.activities.map((activity, idx) => (
                        <div key={idx} className="activity-item">
                          <div
                            className="activity-icon"
                            style={{ backgroundColor: activity.color }}
                          >
                            {activity.type === 'Comments' && <MessageCircle className="activity-icon-svg" />}
                            {activity.type === 'Likes' && <ThumbsUp className="activity-icon-svg" />}
                            {activity.type === 'Shares' && <Share2 className="activity-icon-svg" />}
                            {activity.type === 'Views' && <Eye className="activity-icon-svg" />}
                          </div>
                          <span className="activity-type">{activity.type}</span>
                          <span className="activity-count">{activity.count}</span>
                        </div>
                      ))}
                    </div>
                    {stat.details.teamRank && (
                      <div className="team-rank">
                        <Users className="team-icon" />
                        <span>Team Rank #{stat.details.teamRank}</span>
                      </div>
                    )}
                  </div>
                )}

                {/* Streak Details */}
                {stat.details.currentStreak && (
                  <div className="detail-section">
                    <h4 className="detail-title">Streak Information</h4>
                    <div className="streak-info">
                      <div className="streak-stats">
                        <div className="streak-stat">
                          <span className="streak-label">Current</span>
                          <span className="streak-value">{stat.details.currentStreak} days</span>
                        </div>
                        <div className="streak-stat">
                          <span className="streak-label">Longest</span>
                          <span className="streak-value">{stat.details.longestStreak} days</span>
                        </div>
                        <div className="streak-stat">
                          <span className="streak-label">Next Goal</span>
                          <span className="streak-value">{stat.details.nextMilestone} days</span>
                        </div>
                      </div>
                      <div className="streak-rewards">
                        <span className="rewards-label">Earned Badges:</span>
                        <div className="rewards-list">
                          {stat.details.rewards.map((reward, idx) => (
                            <span key={idx} className="reward-badge">{reward}</span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Impact Details */}
                {stat.details.reachScore && (
                  <div className="detail-section">
                    <h4 className="detail-title">Impact Metrics</h4>
                    <div className="impact-metrics">
                      <div className="impact-scores">
                        <div className="impact-score">
                          <span className="score-label">Reach</span>
                          <span className="score-value">{stat.details.reachScore}%</span>
                        </div>
                        <div className="impact-score">
                          <span className="score-label">Influence</span>
                          <span className="score-value">{stat.details.influenceScore}%</span>
                        </div>
                        <div className="impact-score">
                          <span className="score-label">Positivity</span>
                          <span className="score-value">{stat.details.positivityScore}%</span>
                        </div>
                      </div>
                      <div className="impact-feedback">
                        <Sparkles className="feedback-icon" />
                        <span className="feedback-text">{stat.details.feedback}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}

        {/* Summary Section */}
        {showDetails && currentData.summary && (
          <div className="stats-summary">
            <h3 className="summary-title">
              <Award className="summary-icon" />
              Monthly Summary
            </h3>

            <div className="summary-grid">
              <div className="summary-card achievements">
                <h4 className="summary-card-title">Achievements</h4>
                <div className="achievements-list">
                  {currentData.summary.achievements.map((achievement, idx) => (
                    <span key={idx} className="achievement-badge">{achievement}</span>
                  ))}
                </div>
              </div>

              <div className="summary-card goals">
                <h4 className="summary-card-title">Next Goals</h4>
                <div className="goals-list">
                  {currentData.summary.nextGoals.map((goal, idx) => (
                    <div key={idx} className="goal-item">
                      <div className="goal-info">
                        <span className="goal-text">{goal.goal}</span>
                        <span className="goal-progress">{goal.progress}%</span>
                      </div>
                      <div className="goal-progress-bar">
                        <div
                          className="goal-progress-fill"
                          style={{ width: `${goal.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="summary-card rankings">
                <h4 className="summary-card-title">Rankings</h4>
                <div className="rankings-list">
                  <div className="ranking-item">
                    <Target className="ranking-icon" />
                    <span>Team Position: #{currentData.summary.teamPosition}</span>
                  </div>
                  <div className="ranking-item">
                    <Users className="ranking-icon" />
                    <span>Department Rank: #{currentData.summary.departmentRank}</span>
                  </div>
                  <div className="ranking-item">
                    <Activity className="ranking-icon" />
                    <span>Total Interactions: {currentData.summary.totalInteractions}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MonthlyStats
