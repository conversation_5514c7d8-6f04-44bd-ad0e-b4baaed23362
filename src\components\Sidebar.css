/* Sidebar Component Styles */
.sidebar {
  width: 256px;
  background-color: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.sidebar-header {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.sidebar-nav {
  flex: 1;
  padding: 16px;
}

.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.2s ease;
  text-decoration: none;
  color: #6b7280;
  position: relative;
}

.sidebar-item:hover {
  background-color: #f9fafb;
  color: #111827;
}

.sidebar-item.active {
  background-color: #eff6ff;
  color: #2563eb;
}

.sidebar-item-icon {
  width: 16px;
  height: 16px;
  margin-right: 12px;
}

.sidebar-item-label {
  font-weight: 500;
}

.sidebar-badge {
  margin-left: auto;
  background-color: #3b82f6;
  color: white;
  font-size: 12px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-profile {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.sidebar-profile-content {
  display: flex;
  align-items: center;
}

.sidebar-avatar {
  width: 32px;
  height: 32px;
  background-color: #9ca3af;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-avatar-text {
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.sidebar-profile-info {
  margin-left: 12px;
}

.sidebar-profile-name {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin: 0;
}

.sidebar-profile-title {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}
