/* Enhanced Colorful Sidebar Component Styles */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  overflow: hidden;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
  z-index: 1;
}

/* Enhanced Header */
.sidebar-header {
  padding: 24px 20px;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
}

.logo-sparkle {
  width: 20px;
  height: 20px;
  color: white;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.logo-text {
  flex: 1;
}

.sidebar-title {
  font-size: 20px;
  font-weight: 800;
  color: white;
  margin: 0 0 4px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.sidebar-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.header-decoration {
  display: flex;
  justify-content: center;
  gap: 6px;
}

.decoration-dot {
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.decoration-dot:nth-child(2) {
  animation-delay: 0.3s;
}

.decoration-dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* Enhanced Navigation */
.sidebar-nav {
  flex: 1;
  padding: 20px 16px;
  position: relative;
  z-index: 2;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 32px;
}

.nav-section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 0 16px 0;
  padding: 0 12px;
}

.section-icon {
  width: 14px;
  height: 14px;
}

.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.menu-item-wrapper {
  position: relative;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  font-size: 14px;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--item-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-item:hover {
  transform: translateX(4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.sidebar-item:hover::before {
  opacity: 0.1;
}

.sidebar-item.active {
  background: var(--item-gradient);
  color: white;
  transform: translateX(6px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.sidebar-item.active::before {
  opacity: 1;
}

.item-icon-wrapper {
  position: relative;
  margin-right: 12px;
}

.sidebar-item-icon {
  width: 20px;
  height: 20px;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  background: var(--item-color);
  border-radius: 50%;
  opacity: 0;
  filter: blur(8px);
  transition: opacity 0.3s ease;
}

.sidebar-item:hover .icon-glow,
.sidebar-item.active .icon-glow {
  opacity: 0.3;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sidebar-item-label {
  font-weight: 600;
  font-size: 14px;
}

.sidebar-item-description {
  font-size: 11px;
  opacity: 0.7;
  font-weight: 400;
}

.sidebar-badge {
  margin-left: 8px;
  color: white;
  font-size: 11px;
  font-weight: 700;
  border-radius: 12px;
  padding: 4px 8px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.item-arrow {
  width: 16px;
  height: 16px;
  margin-left: 8px;
  opacity: 0;
  transform: translateX(-4px);
  transition: all 0.3s ease;
}

.sidebar-item:hover .item-arrow,
.sidebar-item.active .item-arrow {
  opacity: 1;
  transform: translateX(0);
}

.active-indicator {
  position: absolute;
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--item-color);
  border-radius: 0 4px 4px 0;
  box-shadow: 0 0 12px var(--item-color);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { left: -20px; opacity: 0; }
  to { left: -16px; opacity: 1; }
}

.bottom-section {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Profile Section */
.sidebar-profile {
  padding: 20px 16px;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(78, 205, 196, 0.2));
  border-radius: 20px 20px 0 0;
}

.profile-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="hexagons" width="30" height="30" patternUnits="userSpaceOnUse"><polygon points="15,5 25,10 25,20 15,25 5,20 5,10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="60" height="60" fill="url(%23hexagons)"/></svg>');
  opacity: 0.3;
}

.sidebar-profile-content {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
  margin-bottom: 16px;
}

.sidebar-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.sidebar-avatar-text {
  color: white;
  font-weight: 700;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.avatar-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #10B981;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
}

.avatar-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.sidebar-profile-info {
  flex: 1;
}

.sidebar-profile-name {
  font-size: 16px;
  font-weight: 700;
  color: white;
  margin: 0 0 4px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.sidebar-profile-title {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 8px 0;
  font-weight: 500;
}

.profile-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 12px;
  height: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.stat-item span {
  font-size: 11px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.logout-icon {
  width: 16px;
  height: 16px;
}

/* Scrollbar Styling */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 260px;
  }

  .sidebar-item {
    padding: 12px 14px;
  }

  .sidebar-item-description {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 240px;
  }

  .sidebar-header {
    padding: 20px 16px;
  }

  .sidebar-title {
    font-size: 18px;
  }

  .sidebar-nav {
    padding: 16px 12px;
  }

  .nav-section {
    margin-bottom: 24px;
  }

  .sidebar-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .sidebar-item-icon {
    width: 18px;
    height: 18px;
  }

  .sidebar-profile {
    padding: 16px 12px;
  }

  .sidebar-avatar {
    width: 40px;
    height: 40px;
  }

  .sidebar-avatar-text {
    font-size: 14px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .sidebar {
    background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
  }

  .sidebar::before {
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  }
}

/* Animation for menu items on load */
.menu-item-wrapper {
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.menu-item-wrapper:nth-child(1) { animation-delay: 0.1s; }
.menu-item-wrapper:nth-child(2) { animation-delay: 0.2s; }
.menu-item-wrapper:nth-child(3) { animation-delay: 0.3s; }
.menu-item-wrapper:nth-child(4) { animation-delay: 0.4s; }
.menu-item-wrapper:nth-child(5) { animation-delay: 0.5s; }
.menu-item-wrapper:nth-child(6) { animation-delay: 0.6s; }
.menu-item-wrapper:nth-child(7) { animation-delay: 0.7s; }
.menu-item-wrapper:nth-child(8) { animation-delay: 0.8s; }
.menu-item-wrapper:nth-child(9) { animation-delay: 0.9s; }
.menu-item-wrapper:nth-child(10) { animation-delay: 1.0s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
