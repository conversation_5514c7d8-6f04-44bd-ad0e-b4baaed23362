import { useState, useEffect } from 'react'
import {
  Plus, Bell, Award, TrendingUp, Users, Star,
  Calendar, Filter, Search, MoreHorizontal,
  Sparkles, Trophy, Target, Zap, Heart,
  BarChart3, PieChart, Activity, Clock
} from 'lucide-react'
import RecognitionFeed from './RecognitionFeed'
import TopRecipients from './TopRecipients'
import MonthlyStats from './MonthlyStats'
import GiveRecognitionModal from './GiveRecognitionModal'
import NotificationsDropdown from './NotificationsDropdown'
import NotificationsPage from './NotificationsPage'
import './MainContent.css'

const MainContent = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)
  const [isNotificationsPageOpen, setIsNotificationsPageOpen] = useState(false)
  const [notificationCount, setNotificationCount] = useState(2)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [currentTime, setCurrentTime] = useState(new Date())
  const [quickStats, setQuickStats] = useState({
    totalRecognitions: 156,
    thisWeek: 23,
    myPoints: 340,
    teamRank: 5
  })
  const [isStatsExpanded, setIsStatsExpanded] = useState(false)

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  // Simulate real-time stats updates
  useEffect(() => {
    const statsTimer = setInterval(() => {
      setQuickStats(prev => ({
        ...prev,
        totalRecognitions: prev.totalRecognitions + Math.floor(Math.random() * 2),
        thisWeek: prev.thisWeek + Math.floor(Math.random() * 2)
      }))
    }, 30000)
    return () => clearInterval(statsTimer)
  }, [])

  const handleOpenModal = () => {
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    // Simulate stats update after recognition
    setQuickStats(prev => ({
      ...prev,
      totalRecognitions: prev.totalRecognitions + 1,
      thisWeek: prev.thisWeek + 1,
      myPoints: prev.myPoints + 25
    }))
  }

  const handleToggleNotifications = () => {
    setIsNotificationsOpen(!isNotificationsOpen)
  }

  const handleCloseNotifications = () => {
    setIsNotificationsOpen(false)
  }

  const handleMarkAllRead = () => {
    setNotificationCount(0)
  }

  const handleViewAllNotifications = () => {
    setIsNotificationsOpen(false)
    setIsNotificationsPageOpen(true)
  }

  const handleCloseNotificationsPage = () => {
    setIsNotificationsPageOpen(false)
  }

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value)
  }

  const handleFilterChange = (type) => {
    setFilterType(type)
  }

  const toggleStatsExpanded = () => {
    setIsStatsExpanded(!isStatsExpanded)
  }

  const getGreeting = () => {
    const hour = currentTime.getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 17) return 'Good afternoon'
    return 'Good evening'
  }

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  return (
    <div className="main-content">
      <div className="main-container">
        {/* Enhanced Header */}
        <div className="main-header">
          <div className="header-left">
            <div className="greeting-section">
              <h1 className="main-title">
                <Sparkles className="title-icon" />
                {getGreeting()}! 👋
              </h1>
              <p className="main-subtitle">
                Ready to celebrate some amazing work today?
              </p>
            </div>
            <div className="time-display">
              <Clock className="time-icon" />
              <span>{formatTime(currentTime)}</span>
            </div>
          </div>
          <div className="header-right">
            <div className="header-actions">
              <div className="search-container">
                <Search className="search-icon" />
                <input
                  type="text"
                  placeholder="Search recognitions..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                />
              </div>
              <div className="notification-bell" onClick={handleToggleNotifications}>
                <Bell className="notification-icon" />
                {notificationCount > 0 && (
                  <span className="notification-badge">
                    {notificationCount}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats Dashboard */}
        <div className="quick-stats-section">
          <div className="stats-header">
            <h2 className="stats-title">
              <BarChart3 className="stats-icon" />
              Quick Overview
            </h2>
            <button
              className="expand-btn"
              onClick={toggleStatsExpanded}
            >
              <MoreHorizontal className="expand-icon" />
            </button>
          </div>
          <div className={`stats-grid ${isStatsExpanded ? 'expanded' : ''}`}>
            <div className="stat-card total-recognitions">
              <div className="stat-icon-wrapper">
                <Award className="stat-icon" />
              </div>
              <div className="stat-content">
                <h3 className="stat-number">{quickStats.totalRecognitions}</h3>
                <p className="stat-label">Total Recognitions</p>
                <span className="stat-trend positive">
                  <TrendingUp className="trend-icon" />
                  +12% this month
                </span>
              </div>
            </div>

            <div className="stat-card weekly-recognitions">
              <div className="stat-icon-wrapper">
                <Calendar className="stat-icon" />
              </div>
              <div className="stat-content">
                <h3 className="stat-number">{quickStats.thisWeek}</h3>
                <p className="stat-label">This Week</p>
                <span className="stat-trend positive">
                  <TrendingUp className="trend-icon" />
                  +5 from last week
                </span>
              </div>
            </div>

            <div className="stat-card my-points">
              <div className="stat-icon-wrapper">
                <Star className="stat-icon" />
              </div>
              <div className="stat-content">
                <h3 className="stat-number">{quickStats.myPoints}</h3>
                <p className="stat-label">My Points</p>
                <span className="stat-trend positive">
                  <Trophy className="trend-icon" />
                  Rank #{quickStats.teamRank}
                </span>
              </div>
            </div>

            <div className="stat-card team-activity">
              <div className="stat-icon-wrapper">
                <Users className="stat-icon" />
              </div>
              <div className="stat-content">
                <h3 className="stat-number">42</h3>
                <p className="stat-label">Active Team Members</p>
                <span className="stat-trend neutral">
                  <Activity className="trend-icon" />
                  85% engagement
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Give Recognition Section */}
        <div className="give-recognition-section">
          <div className="recognition-header">
            <div className="recognition-title">
              <Heart className="heart-icon" />
              <span>Spread Some Love</span>
            </div>
            <button onClick={handleOpenModal} className="give-recognition-btn">
              <Plus className="give-recognition-icon" />
              Give Recognition
              <Sparkles className="sparkle-icon" />
            </button>
          </div>
          <div className="recognition-quick-actions">
            <button
              className="quick-action-btn teamwork"
              onClick={() => {
                handleOpenModal()
                // Could pre-select teamwork category
              }}
            >
              <Users className="action-icon" />
              <span>Teamwork</span>
            </button>
            <button
              className="quick-action-btn innovation"
              onClick={() => {
                handleOpenModal()
                // Could pre-select innovation category
              }}
            >
              <Zap className="action-icon" />
              <span>Innovation</span>
            </button>
            <button
              className="quick-action-btn excellence"
              onClick={() => {
                handleOpenModal()
                // Could pre-select excellence category
              }}
            >
              <Target className="action-icon" />
              <span>Excellence</span>
            </button>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="filter-section">
          <div className="filter-header">
            <h3 className="filter-title">
              <Filter className="filter-icon" />
              Filter Recognitions
            </h3>
          </div>
          <div className="filter-controls">
            <button
              className={`filter-btn ${filterType === 'all' ? 'active' : ''}`}
              onClick={() => handleFilterChange('all')}
            >
              All
            </button>
            <button
              className={`filter-btn ${filterType === 'recent' ? 'active' : ''}`}
              onClick={() => handleFilterChange('recent')}
            >
              Recent
            </button>
            <button
              className={`filter-btn ${filterType === 'team' ? 'active' : ''}`}
              onClick={() => handleFilterChange('team')}
            >
              My Team
            </button>
            <button
              className={`filter-btn ${filterType === 'trending' ? 'active' : ''}`}
              onClick={() => handleFilterChange('trending')}
            >
              Trending
            </button>
          </div>
        </div>

        {/* Main Grid Layout */}
        <div className="main-grid">
          {/* Left Column */}
          <div className="main-left-column">
            <RecognitionFeed />
          </div>

          {/* Right Column */}
          <div className="main-right-column">
            <TopRecipients />
            <MonthlyStats />
          </div>
        </div>
      </div>

      {/* Give Recognition Modal */}
      <GiveRecognitionModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />

      {/* Notifications Dropdown */}
      <NotificationsDropdown
        isOpen={isNotificationsOpen}
        onClose={handleCloseNotifications}
        notificationCount={notificationCount}
        onMarkAllRead={handleMarkAllRead}
        onViewAll={handleViewAllNotifications}
      />

      {/* Notifications Page */}
      <NotificationsPage
        isOpen={isNotificationsPageOpen}
        onClose={handleCloseNotificationsPage}
      />
    </div>
  )
}

export default MainContent
