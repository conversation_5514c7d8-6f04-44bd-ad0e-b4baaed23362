/* Recognition Demo Styles */
.recognition-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 48px;
}

.demo-header h1 {
  font-size: 48px;
  font-weight: 800;
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 16px 0;
}

.demo-header p {
  font-size: 20px;
  color: #6b7280;
  margin: 0;
}

.demo-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  color: #4f46e5;
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  color: white;
  padding: 12px;
  border-radius: 12px;
}

.stat-content h3 {
  font-size: 36px;
  font-weight: 800;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.stat-content p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

.demo-actions {
  text-align: center;
  margin-bottom: 48px;
}

.give-recognition-btn {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.give-recognition-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
}

.btn-icon {
  width: 20px;
  height: 20px;
}

.recent-recognitions {
  margin-top: 48px;
}

.recent-recognitions h2 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 24px 0;
  text-align: center;
}

.recognition-list {
  display: grid;
  gap: 20px;
}

.recognition-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.recognition-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.recognition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.recognition-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
}

.sender {
  color: #4f46e5;
}

.arrow {
  color: #9ca3af;
  font-size: 18px;
}

.recipient {
  color: #1f2937;
}

.recognition-time {
  color: #6b7280;
  font-size: 14px;
}

.recognition-content {
  display: grid;
  gap: 12px;
}

.recognition-category {
  display: inline-block;
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  width: fit-content;
}

.recognition-message {
  color: #374151;
  font-size: 16px;
  line-height: 1.5;
}

.recognition-points {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #f59e0b;
  font-weight: 600;
}

.points-icon {
  width: 16px;
  height: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .recognition-demo {
    padding: 20px 16px;
  }

  .demo-header h1 {
    font-size: 36px;
  }

  .demo-header p {
    font-size: 18px;
  }

  .demo-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 24px;
  }

  .give-recognition-btn {
    padding: 14px 28px;
    font-size: 16px;
  }

  .recognition-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
