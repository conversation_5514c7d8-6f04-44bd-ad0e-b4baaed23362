/* Enhanced MainContent Component Styles */
.main-content {
  flex: 1;
  overflow: auto;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  position: relative;
}

.main-content::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(79, 70, 229, 0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
  z-index: 0;
}

.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
  z-index: 1;
}

.main-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.greeting-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.main-title {
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  from { filter: brightness(1); }
  to { filter: brightness(1.1); }
}

.title-icon {
  width: 24px;
  height: 24px;
  color: #fbbf24;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.main-subtitle {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 6px 12px;
  border-radius: 8px;
  color: #475569;
  font-weight: 600;
  font-size: 12px;
  border: 1px solid #e2e8f0;
}

.time-icon {
  width: 14px;
  height: 14px;
  color: #6366f1;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 18px;
  height: 18px;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  padding: 8px 8px 8px 32px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 12px;
  width: 200px;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.notification-bell {
  position: relative;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #e2e8f0;
}

.notification-bell:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  border-color: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.notification-bell:hover .notification-icon {
  color: white;
}

.notification-icon {
  width: 20px;
  height: 20px;
  color: #64748b;
  transition: color 0.3s ease;
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  font-size: 10px;
  font-weight: 600;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Quick Stats Section */
.quick-stats-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.stats-icon {
  width: 18px;
  height: 18px;
  color: #4f46e5;
}

.expand-btn {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.expand-btn:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  border-color: #4f46e5;
  transform: translateY(-1px);
}

.expand-btn:hover .expand-icon {
  color: white;
}

.expand-icon {
  width: 20px;
  height: 20px;
  color: #64748b;
  transition: color 0.3s ease;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  transition: all 0.5s ease;
}

.stats-grid.expanded {
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
}

.stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 16px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(79, 70, 229, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card.total-recognitions {
  border-color: #4f46e5;
}

.stat-card.weekly-recognitions {
  border-color: #10b981;
}

.stat-card.my-points {
  border-color: #f59e0b;
}

.stat-card.team-activity {
  border-color: #8b5cf6;
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  position: relative;
  z-index: 1;
}

.total-recognitions .stat-icon-wrapper {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
}

.weekly-recognitions .stat-icon-wrapper {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.my-points .stat-icon-wrapper {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.team-activity .stat-icon-wrapper {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.stat-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.stat-content {
  position: relative;
  z-index: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 4px 0;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  margin: 0 0 6px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 6px;
  width: fit-content;
}

.stat-trend.positive {
  color: #059669;
  background: rgba(16, 185, 129, 0.1);
}

.stat-trend.neutral {
  color: #7c3aed;
  background: rgba(139, 92, 246, 0.1);
}

.trend-icon {
  width: 10px;
  height: 10px;
}

/* Enhanced Give Recognition Section */
.give-recognition-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.recognition-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.recognition-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
}

.heart-icon {
  width: 18px;
  height: 18px;
  color: #ec4899;
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.give-recognition-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 700;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
  position: relative;
  overflow: hidden;
}

.give-recognition-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.give-recognition-btn:hover::before {
  left: 100%;
}

.give-recognition-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
}

.give-recognition-icon, .sparkle-icon {
  width: 16px;
  height: 16px;
}

.sparkle-icon {
  animation: sparkleRotate 3s linear infinite;
}

@keyframes sparkleRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.recognition-quick-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 8px;
  border: 2px solid transparent;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #64748b;
  font-weight: 600;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-btn.teamwork:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  border-color: #4f46e5;
  color: white;
  transform: translateY(-2px);
}

.quick-action-btn.innovation:hover {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  color: white;
  transform: translateY(-2px);
}

.quick-action-btn.excellence:hover {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: #f59e0b;
  color: white;
  transform: translateY(-2px);
}

.action-icon {
  width: 14px;
  height: 14px;
}

/* Filter Section */
.filter-section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.filter-header {
  margin-bottom: 12px;
}

.filter-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.filter-icon {
  width: 16px;
  height: 16px;
  color: #4f46e5;
}

.filter-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 6px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #64748b;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
  transform: translateY(-1px);
}

.filter-btn.active {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  border-color: #4f46e5;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.main-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

@media (min-width: 1280px) {
  .main-grid {
    grid-template-columns: 3fr 1fr;
  }
}

.main-left-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.main-right-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-container {
    padding: 16px;
  }

  .main-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .header-actions {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .search-input {
    width: 100%;
  }

  .main-title {
    font-size: 24px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
  }

  .stat-card {
    padding: 12px;
  }

  .give-recognition-section {
    padding: 16px;
  }

  .recognition-quick-actions {
    gap: 8px;
  }

  .quick-action-btn {
    padding: 6px 12px;
    font-size: 11px;
  }

  .filter-section {
    padding: 12px;
  }

  .main-grid {
    gap: 16px;
  }
}
