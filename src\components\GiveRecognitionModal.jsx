 import { useState } from 'react'
import { X, Search, Upload, Image, Smile, Users, Award, Lightbulb, Crown, Heart } from 'lucide-react'
import EmojiPicker from './EmojiPicker'
import './GiveRecognitionModal.css'

const GiveRecognitionModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    recipients: [],
    category: '',
    message: '',
    visibility: 'public',
    attachments: []
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [showSuccess, setShowSuccess] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  const colleagues = [
    { id: 1, name: '<PERSON>', department: 'Engineering', avatar: 'SC' },
    { id: 2, name: '<PERSON>', department: 'Design', avatar: 'MT' },
    { id: 3, name: '<PERSON>', department: 'Marketing', avatar: 'E<PERSON>' },
    { id: 4, name: '<PERSON>', department: 'Sales', avatar: 'DK' },
    { id: 5, name: '<PERSON>', department: 'HR', avatar: 'LW' },
    { id: 6, name: '<PERSON>', department: 'Engineering', avatar: 'AM' },
    { id: 7, name: 'James Wilson', department: 'Operations', avatar: 'JW' },
  ]

  const categories = [
    { value: 'teamwork', label: 'Great Teamwork', icon: Users },
    { value: 'above-beyond', label: 'Going Above & Beyond', icon: Award },
    { value: 'innovation', label: 'Innovation', icon: Lightbulb },
    { value: 'leadership', label: 'Leadership', icon: Crown },
    { value: 'customer-focus', label: 'Customer Focus', icon: Heart },
  ]

  const filteredColleagues = colleagues.filter(colleague =>
    colleague.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    colleague.department.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleRecipientToggle = (colleague) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.find(r => r.id === colleague.id)
        ? prev.recipients.filter(r => r.id !== colleague.id)
        : [...prev.recipients, colleague]
    }))
  }

  const handleEmojiSelect = (emoji) => {
    setFormData(prev => ({
      ...prev,
      message: prev.message + emoji
    }))
  }

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files)
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (formData.recipients.length === 0 || !formData.category || !formData.message.trim()) {
      alert('Please fill in all required fields')
      return
    }

    // Simulate API call
    setTimeout(() => {
      setShowSuccess(true)
      setTimeout(() => {
        setShowSuccess(false)
        onClose()
        setFormData({
          recipients: [],
          category: '',
          message: '',
          visibility: 'public',
          attachments: []
        })
        setSearchTerm('')
      }, 2000)
    }, 500)
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        {showSuccess ? (
          <div className="success-message">
            <div className="success-icon">✅</div>
            <h3>Recognition sent!</h3>
            <p>You just made someone's day brighter.</p>
          </div>
        ) : (
          <>
            <div className="modal-header">
              <h2>Give Recognition</h2>
              <button onClick={onClose} className="close-button">
                <X className="close-icon" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="modal-form">
              {/* Recipients */}
              <div className="form-group">
                <label className="form-label">
                  Recipient(s) <span className="required">*</span>
                </label>
                <div className="search-container">
                  <Search className="search-icon" />
                  <input
                    type="text"
                    placeholder="Search colleagues..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="search-input"
                  />
                </div>
                <div className="recipients-list">
                  {filteredColleagues.map(colleague => (
                    <div
                      key={colleague.id}
                      onClick={() => handleRecipientToggle(colleague)}
                      className={`recipient-item ${formData.recipients.find(r => r.id === colleague.id) ? 'selected' : ''}`}
                    >
                      <div className="recipient-avatar">
                        {colleague.avatar}
                      </div>
                      <div className="recipient-info">
                        <span className="recipient-name">{colleague.name}</span>
                        <span className="recipient-department">{colleague.department}</span>
                      </div>
                    </div>
                  ))}
                </div>
                {formData.recipients.length > 0 && (
                  <div className="selected-recipients">
                    <span className="selected-label">Selected:</span>
                    {formData.recipients.map(recipient => (
                      <span key={recipient.id} className="selected-tag">
                        {recipient.name}
                        <button
                          type="button"
                          onClick={() => handleRecipientToggle(recipient)}
                          className="remove-tag"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Category */}
              <div className="form-group">
                <label className="form-label">
                  Category / Reason <span className="required">*</span>
                </label>
                <div className="category-grid">
                  {categories.map(category => (
                    <button
                      key={category.value}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, category: category.value }))}
                      className={`category-item ${formData.category === category.value ? 'selected' : ''}`}
                    >
                      <category.icon className="category-icon" />
                      <span>{category.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Message */}
              <div className="form-group">
                <label className="form-label">
                  Message <span className="required">*</span>
                </label>
                <textarea
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Write your message of appreciation…"
                  className="message-textarea"
                  rows={4}
                />
              </div>

              {/* Optional Features */}
              <div className="form-group">
                <label className="form-label">Add Fun Elements (Optional)</label>
                <div className="optional-features">
                  <button
                    type="button"
                    className="feature-button"
                    onClick={() => setShowEmojiPicker(true)}
                  >
                    <Smile className="feature-icon" />
                    Add Emoji/GIF
                  </button>
                  <label className="feature-button">
                    <Image className="feature-icon" />
                    Add Image
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                    />
                  </label>
                  <label className="feature-button">
                    <Upload className="feature-icon" />
                    Attach File
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                    />
                  </label>
                </div>
                {formData.attachments.length > 0 && (
                  <div className="attachments-list">
                    {formData.attachments.map((file, index) => (
                      <span key={index} className="attachment-item">
                        📎 {file.name}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Visibility */}
              <div className="form-group">
                <label className="form-label">Visibility</label>
                <div className="visibility-options">
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="public"
                      checked={formData.visibility === 'public'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Public (visible to all)</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="team"
                      checked={formData.visibility === 'team'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Team Only</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="private"
                      checked={formData.visibility === 'private'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Private (visible to recipient only)</span>
                  </label>
                </div>
              </div>

              {/* Submit Button */}
              <div className="form-actions">
                <button type="button" onClick={onClose} className="cancel-button">
                  Cancel
                </button>
                <button type="submit" className="submit-button">
                  Send Recognition
                </button>
              </div>
            </form>
          </>
        )}
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelect={handleEmojiSelect}
          onClose={() => setShowEmojiPicker(false)}
        />
      )}
    </div>
  )
}

export default GiveRecognitionModal
