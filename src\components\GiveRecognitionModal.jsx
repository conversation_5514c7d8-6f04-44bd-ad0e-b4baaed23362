import { useState } from 'react'
import {
  X, Heart, <PERSON>rkles, Trophy, Users, Zap, Crown,
  Smile, Upload, Image, Search, Target, Lightbulb
} from 'lucide-react'
import EmojiPicker from './EmojiPicker'
import './GiveRecognitionModal.css'

const GiveRecognitionModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    recipients: [],
    category: '',
    message: '',
    visibility: 'public',
    attachments: []
  })
  const [showSuccess, setShowSuccess] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  // Enhanced team data with colors and roles
  const colleagues = [
    { id: 1, name: '<PERSON>', department: 'Engineering', role: 'Senior Developer', avatar: 'SC', color: '#FF6B9D', status: 'online', points: 1250 },
    { id: 2, name: '<PERSON>', department: 'Design', role: 'UI/UX Designer', avatar: 'MT', color: '#4ECDC4', status: 'away', points: 980 },
    { id: 3, name: '<PERSON>', department: 'Marketing', role: 'Marketing Manager', avatar: 'EJ', color: '#45B7D1', status: 'online', points: 1100 },
    { id: 4, name: 'David Kim', department: 'Engineering', role: 'DevOps Engineer', avatar: 'DK', color: '#96CEB4', status: 'busy', points: 890 },
    { id: 5, name: 'Lisa Wang', department: 'Product', role: 'Product Manager', avatar: 'LW', color: '#FFEAA7', status: 'online', points: 1350 },
    { id: 6, name: 'Alex Morgan', department: 'Sales', role: 'Sales Director', avatar: 'AM', color: '#DDA0DD', status: 'offline', points: 750 },
    { id: 7, name: 'Rachel Green', department: 'HR', role: 'HR Specialist', avatar: 'RG', color: '#FFB6C1', status: 'online', points: 920 },
    { id: 8, name: 'James Wilson', department: 'Finance', role: 'Financial Analyst', avatar: 'JW', color: '#87CEEB', status: 'away', points: 680 }
  ]

  // Vibrant recognition categories with themes
  const categories = [
    { id: 'teamwork', label: '🤝 Outstanding Teamwork', icon: Users, color: '#FF6B9D', gradient: 'linear-gradient(135deg, #FF6B9D, #C44569)', description: 'Exceptional collaboration and team spirit' },
    { id: 'innovation', label: '💡 Innovation Excellence', icon: Lightbulb, color: '#4ECDC4', gradient: 'linear-gradient(135deg, #4ECDC4, #44A08D)', description: 'Creative thinking and breakthrough ideas' },
    { id: 'leadership', label: '👑 Leadership Impact', icon: Crown, color: '#45B7D1', gradient: 'linear-gradient(135deg, #45B7D1, #2980B9)', description: 'Inspiring and guiding others to success' },
    { id: 'achievement', label: '🏆 Outstanding Achievement', icon: Trophy, color: '#96CEB4', gradient: 'linear-gradient(135deg, #96CEB4, #6AB7AA)', description: 'Exceptional results and goal completion' },
    { id: 'dedication', label: '⚡ Dedication & Focus', icon: Zap, color: '#FFEAA7', gradient: 'linear-gradient(135deg, #FFEAA7, #FDCB6E)', description: 'Unwavering commitment and hard work' },
    { id: 'mentorship', label: '🎯 Amazing Mentorship', icon: Target, color: '#DDA0DD', gradient: 'linear-gradient(135deg, #DDA0DD, #DA70D6)', description: 'Helping others grow and develop' },
    { id: 'creativity', label: '🎨 Creative Excellence', icon: Sparkles, color: '#FFB6C1', gradient: 'linear-gradient(135deg, #FFB6C1, #FF91A4)', description: 'Artistic vision and creative solutions' },
    { id: 'customer', label: '❤️ Customer Champion', icon: Heart, color: '#87CEEB', gradient: 'linear-gradient(135deg, #87CEEB, #5DADE2)', description: 'Going above and beyond for customers' }
  ]



  // Handler functions
  const handleRecipientToggle = (colleague) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.find(r => r.id === colleague.id)
        ? prev.recipients.filter(r => r.id !== colleague.id)
        : [...prev.recipients, colleague]
    }))
  }

  const handleEmojiSelect = (emoji) => {
    setFormData(prev => ({
      ...prev,
      message: prev.message + emoji
    }))
    setShowEmojiPicker(false)
  }

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files)
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (formData.recipients.length === 0 || !formData.category || !formData.message.trim()) {
      alert('Please fill in all required fields')
      return
    }

    // Simulate API call
    setTimeout(() => {
      setShowSuccess(true)
      setTimeout(() => {
        setShowSuccess(false)
        onClose()
        setFormData({
          recipients: [],
          category: '',
          message: '',
          visibility: 'public',
          attachments: []
        })
        setSearchTerm('')
      }, 2000)
    }, 500)
  }

  const filteredColleagues = colleagues.filter(colleague =>
    colleague.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    colleague.department.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (!isOpen) return null

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        {showSuccess ? (
          <div className="success-message">
            <div className="success-icon">✅</div>
            <h3>Recognition sent!</h3>
            <p>You just made someone's day brighter.</p>
          </div>
        ) : (
          <>
            <div className="modal-header">
              <h2>Give Recognition</h2>
              <button onClick={onClose} className="close-button">
                <X className="close-icon" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="modal-form">
              {/* Recipients */}
              <div className="form-group">
                <label className="form-label">
                  Recipient(s) <span className="required">*</span>
                </label>
                <div className="search-container">
                  <Search className="search-icon" />
                  <input
                    type="text"
                    placeholder="Search colleagues..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="search-input"
                  />
                </div>
                <div className="recipients-list">
                  {filteredColleagues.map(colleague => (
                    <div
                      key={colleague.id}
                      onClick={() => handleRecipientToggle(colleague)}
                      className={`recipient-item ${formData.recipients.find(r => r.id === colleague.id) ? 'selected' : ''}`}
                    >
                      <div className="recipient-avatar">
                        {colleague.avatar}
                      </div>
                      <div className="recipient-info">
                        <span className="recipient-name">{colleague.name}</span>
                        <span className="recipient-department">{colleague.department}</span>
                      </div>
                    </div>
                  ))}
                </div>
                {formData.recipients.length > 0 && (
                  <div className="selected-recipients">
                    <span className="selected-label">Selected:</span>
                    {formData.recipients.map(recipient => (
                      <span key={recipient.id} className="selected-tag">
                        {recipient.name}
                        <button
                          type="button"
                          onClick={() => handleRecipientToggle(recipient)}
                          className="remove-tag"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Category */}
              <div className="form-group">
                <label className="form-label">
                  Category / Reason <span className="required">*</span>
                </label>
                <div className="category-grid">
                  {categories.map(category => {
                    const IconComponent = category.icon
                    return (
                      <button
                        key={category.id}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, category: category.id }))}
                        className={`category-item ${formData.category === category.id ? 'selected' : ''}`}
                      >
                        <IconComponent className="category-icon" />
                        <span>{category.label}</span>
                      </button>
                    )
                  })}
                </div>
              </div>

              {/* Message */}
              <div className="form-group">
                <label className="form-label">
                  Message <span className="required">*</span>
                </label>
                <textarea
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Write your message of appreciation…"
                  className="message-textarea"
                  rows={4}
                />
              </div>

              {/* Optional Features */}
              <div className="form-group">
                <label className="form-label">Add Fun Elements (Optional)</label>
                <div className="optional-features">
                  <button
                    type="button"
                    className="feature-button"
                    onClick={() => setShowEmojiPicker(true)}
                  >
                    <Smile className="feature-icon" />
                    Add Emoji/GIF
                  </button>
                  <label className="feature-button">
                    <Image className="feature-icon" />
                    Add Image
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                    />
                  </label>
                  <label className="feature-button">
                    <Upload className="feature-icon" />
                    Attach File
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                    />
                  </label>
                </div>
                {formData.attachments.length > 0 && (
                  <div className="attachments-list">
                    {formData.attachments.map((file, index) => (
                      <span key={index} className="attachment-item">
                        📎 {file.name}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Visibility */}
              <div className="form-group">
                <label className="form-label">Visibility</label>
                <div className="visibility-options">
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="public"
                      checked={formData.visibility === 'public'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Public (visible to all)</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="team"
                      checked={formData.visibility === 'team'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Team Only</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="private"
                      checked={formData.visibility === 'private'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Private (visible to recipient only)</span>
                  </label>
                </div>
              </div>

              {/* Submit Button */}
              <div className="form-actions">
                <button type="button" onClick={onClose} className="cancel-button">
                  Cancel
                </button>
                <button type="submit" className="submit-button">
                  Send Recognition
                </button>
              </div>
            </form>
          </>
        )}
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelect={handleEmojiSelect}
          onClose={() => setShowEmojiPicker(false)}
        />
      )}
    </div>
  )
}

export default GiveRecognitionModal
