import { useState, useEffect } from 'react'
import {
  X, Heart, Star, Send, Spark<PERSON>, Trophy, Users, Zap, Crown, Gift,
  Smile, Calendar, Eye, Upload, Image, Search, Filter, MapPin,
  CheckCircle, Target, Lightbulb, Award, Coffee, Music, Camera,
  ThumbsUp, MessageCircle, Share2, Bookmark, Clock, Plus, Minus
} from 'lucide-react'
import EmojiPicker from './EmojiPicker'
import './GiveRecognitionModal.css'

const GiveRecognitionModal = ({ isOpen, onClose }) => {
  // Enhanced state management with step-by-step flow
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedPeople, setSelectedPeople] = useState([])
  const [selectedCategory, setSelectedCategory] = useState('')
  const [message, setMessage] = useState('')
  const [points, setPoints] = useState(50)
  const [showSuccess, setShowSuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTheme, setSelectedTheme] = useState('vibrant')
  const [isScheduled, setIsScheduled] = useState(false)
  const [scheduledDate, setScheduledDate] = useState('')
  const [visibility, setVisibility] = useState('public')
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [attachments, setAttachments] = useState([])
  const [animatePoints, setAnimatePoints] = useState(false)
  const [selectedTags, setSelectedTags] = useState([])
  const [customBadge, setCustomBadge] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('all')
  const [recognitionType, setRecognitionType] = useState('individual')

  // Enhanced team data with colors and roles
  const colleagues = [
    { id: 1, name: 'Sarah Chen', department: 'Engineering', role: 'Senior Developer', avatar: 'SC', color: '#FF6B9D', status: 'online', points: 1250 },
    { id: 2, name: 'Michael Torres', department: 'Design', role: 'UI/UX Designer', avatar: 'MT', color: '#4ECDC4', status: 'away', points: 980 },
    { id: 3, name: 'Emily Johnson', department: 'Marketing', role: 'Marketing Manager', avatar: 'EJ', color: '#45B7D1', status: 'online', points: 1100 },
    { id: 4, name: 'David Kim', department: 'Engineering', role: 'DevOps Engineer', avatar: 'DK', color: '#96CEB4', status: 'busy', points: 890 },
    { id: 5, name: 'Lisa Wang', department: 'Product', role: 'Product Manager', avatar: 'LW', color: '#FFEAA7', status: 'online', points: 1350 },
    { id: 6, name: 'Alex Morgan', department: 'Sales', role: 'Sales Director', avatar: 'AM', color: '#DDA0DD', status: 'offline', points: 750 },
    { id: 7, name: 'Rachel Green', department: 'HR', role: 'HR Specialist', avatar: 'RG', color: '#FFB6C1', status: 'online', points: 920 },
    { id: 8, name: 'James Wilson', department: 'Finance', role: 'Financial Analyst', avatar: 'JW', color: '#87CEEB', status: 'away', points: 680 }
  ]

  // Enhanced vibrant recognition categories with themes and animations
  const categories = [
    { id: 'teamwork', label: '🤝 Outstanding Teamwork', icon: Users, color: '#FF6B9D', gradient: 'linear-gradient(135deg, #FF6B9D, #C44569)', description: 'Exceptional collaboration and team spirit', points: 75, badge: '🏅' },
    { id: 'innovation', label: '💡 Innovation Excellence', icon: Lightbulb, color: '#4ECDC4', gradient: 'linear-gradient(135deg, #4ECDC4, #44A08D)', description: 'Creative thinking and breakthrough ideas', points: 100, badge: '🚀' },
    { id: 'leadership', label: '👑 Leadership Impact', icon: Crown, color: '#45B7D1', gradient: 'linear-gradient(135deg, #45B7D1, #2980B9)', description: 'Inspiring and guiding others to success', points: 90, badge: '⭐' },
    { id: 'achievement', label: '🏆 Outstanding Achievement', icon: Trophy, color: '#96CEB4', gradient: 'linear-gradient(135deg, #96CEB4, #6AB7AA)', description: 'Exceptional results and goal completion', points: 85, badge: '🎯' },
    { id: 'dedication', label: '⚡ Dedication & Focus', icon: Zap, color: '#FFEAA7', gradient: 'linear-gradient(135deg, #FFEAA7, #FDCB6E)', description: 'Unwavering commitment and hard work', points: 70, badge: '💪' },
    { id: 'mentorship', label: '🎯 Amazing Mentorship', icon: Target, color: '#DDA0DD', gradient: 'linear-gradient(135deg, #DDA0DD, #DA70D6)', description: 'Helping others grow and develop', points: 80, badge: '🌟' },
    { id: 'creativity', label: '🎨 Creative Excellence', icon: Sparkles, color: '#FFB6C1', gradient: 'linear-gradient(135deg, #FFB6C1, #FF91A4)', description: 'Artistic vision and creative solutions', points: 75, badge: '🎨' },
    { id: 'customer', label: '❤️ Customer Champion', icon: Heart, color: '#87CEEB', gradient: 'linear-gradient(135deg, #87CEEB, #5DADE2)', description: 'Going above and beyond for customers', points: 85, badge: '💖' },
    { id: 'problem-solving', label: '🧩 Problem Solver', icon: Target, color: '#FF9A8B', gradient: 'linear-gradient(135deg, #FF9A8B, #FF6B6B)', description: 'Finding solutions to complex challenges', points: 90, badge: '🔧' },
    { id: 'communication', label: '📢 Great Communicator', icon: MessageCircle, color: '#A8E6CF', gradient: 'linear-gradient(135deg, #A8E6CF, #88D8A3)', description: 'Clear and effective communication', points: 65, badge: '💬' },
    { id: 'collaboration', label: '🤝 Team Collaboration', icon: Coffee, color: '#FFD93D', gradient: 'linear-gradient(135deg, #FFD93D, #FFC107)', description: 'Working together seamlessly', points: 70, badge: '☕' },
    { id: 'excellence', label: '🌟 Excellence in Work', icon: Award, color: '#B19CD9', gradient: 'linear-gradient(135deg, #B19CD9, #9B59B6)', description: 'Consistently delivering high-quality work', points: 95, badge: '💎' }
  ]

  // Recognition tags for additional categorization
  const recognitionTags = [
    { id: 'urgent', label: 'Urgent Help', color: '#FF6B6B', emoji: '🚨' },
    { id: 'milestone', label: 'Milestone Achievement', color: '#4ECDC4', emoji: '🎯' },
    { id: 'overtime', label: 'Extra Hours', color: '#45B7D1', emoji: '⏰' },
    { id: 'weekend', label: 'Weekend Work', color: '#96CEB4', emoji: '📅' },
    { id: 'learning', label: 'Quick Learning', color: '#FFEAA7', emoji: '📚' },
    { id: 'positive', label: 'Positive Attitude', color: '#DDA0DD', emoji: '😊' }
  ]

  // Quick message templates with categories
  const quickMessages = [
    { text: "Amazing work on the project! Your dedication really shows. 🌟", category: 'achievement', tags: ['milestone'] },
    { text: "Thank you for being such an incredible team player! 🤝", category: 'teamwork', tags: ['collaboration'] },
    { text: "Your innovative approach solved a complex problem beautifully! 💡", category: 'innovation', tags: ['problem-solving'] },
    { text: "Your leadership during the crisis was truly inspiring! 👑", category: 'leadership', tags: ['urgent'] },
    { text: "The extra hours you put in made all the difference! ⚡", category: 'dedication', tags: ['overtime'] },
    { text: "Thank you for mentoring the new team members so well! 🎯", category: 'mentorship', tags: ['learning'] },
    { text: "Your creative solution exceeded all expectations! 🎨", category: 'creativity', tags: ['innovation'] },
    { text: "Going above and beyond for our customers! ❤️", category: 'customer', tags: ['excellence'] }
  ]

  // Theme options for visual customization
  const themes = {
    vibrant: { primary: '#FF6B9D', secondary: '#4ECDC4', accent: '#FFEAA7' },
    ocean: { primary: '#0077BE', secondary: '#00A8CC', accent: '#00C9A7' },
    sunset: { primary: '#FF6B6B', secondary: '#FFA726', accent: '#FFCC02' },
    forest: { primary: '#2E7D32', secondary: '#43A047', accent: '#8BC34A' },
    royal: { primary: '#673AB7', secondary: '#9C27B0', accent: '#E91E63' }
  }



  // Enhanced handler functions with new features
  const handlePersonToggle = (person) => {
    setSelectedPeople(prev =>
      prev.find(p => p.id === person.id)
        ? prev.filter(p => p.id !== person.id)
        : [...prev, person]
    )
  }

  const handleCategorySelect = (categoryId) => {
    setSelectedCategory(categoryId)
    const category = categories.find(c => c.id === categoryId)
    if (category) {
      setPoints(category.points)
      setAnimatePoints(true)
      setTimeout(() => setAnimatePoints(false), 600)

      // Auto-suggest message based on category
      const suggestedMessage = quickMessages.find(m => m.category === categoryId)
      if (suggestedMessage && !message) {
        setMessage(suggestedMessage.text)
      }
    }
  }

  const handleQuickMessage = (messageText, tags = []) => {
    setMessage(messageText)
    setSelectedTags(tags)
  }

  const handleEmojiSelect = (emoji) => {
    setMessage(prev => prev + emoji)
    setShowEmojiPicker(false)
  }

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files)
    setAttachments(prev => [...prev, ...files])
  }

  const handleTagToggle = (tagId) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(t => t !== tagId)
        : [...prev, tagId]
    )
  }

  const handleThemeChange = (themeName) => {
    setSelectedTheme(themeName)
  }

  const handleStepNavigation = (direction) => {
    if (direction === 'next' && currentStep < 4) {
      setCurrentStep(prev => prev + 1)
    } else if (direction === 'prev' && currentStep > 1) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const handlePreview = () => {
    setShowPreview(!showPreview)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (selectedPeople.length === 0 || !selectedCategory || !message.trim()) {
      alert('Please fill in all required fields')
      return
    }

    setIsSubmitting(true)

    // Simulate API call with enhanced feedback
    await new Promise(resolve => setTimeout(resolve, 2000))

    setIsSubmitting(false)
    setShowSuccess(true)

    // Auto close after 3 seconds
    setTimeout(() => {
      setShowSuccess(false)
      onClose()
      // Reset all form data
      resetForm()
    }, 3000)
  }

  const resetForm = () => {
    setCurrentStep(1)
    setSelectedPeople([])
    setSelectedCategory('')
    setMessage('')
    setPoints(50)
    setSearchTerm('')
    setIsScheduled(false)
    setScheduledDate('')
    setVisibility('public')
    setShowEmojiPicker(false)
    setShowPreview(false)
    setAttachments([])
    setSelectedTags([])
    setCustomBadge('')
    setSelectedDepartment('all')
    setRecognitionType('individual')
  }

  // Enhanced filtering with department and search
  const filteredColleagues = colleagues.filter(colleague => {
    const matchesSearch = colleague.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         colleague.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         colleague.role.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDepartment = selectedDepartment === 'all' || colleague.department === selectedDepartment
    return matchesSearch && matchesDepartment
  })

  // Get unique departments for filtering
  const departments = ['all', ...new Set(colleagues.map(c => c.department))]

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      resetForm()
    }
  }, [isOpen])

  if (!isOpen) return null

  const selectedCategoryData = categories.find(cat => cat.id === selectedCategory)
  const currentTheme = themes[selectedTheme]

  return (
    <div className="modal-overlay" style={{ background: `linear-gradient(135deg, ${currentTheme.primary}15, ${currentTheme.secondary}15)` }}>
      <div className="modal-container" style={{ borderTop: `4px solid ${currentTheme.primary}` }}>
        {showSuccess ? (
          <div className="success-screen">
            <div className="success-animation">
              <div className="success-circle" style={{ background: selectedCategoryData?.gradient }}>
                <CheckCircle className="success-icon" />
              </div>
              <div className="confetti-animation">
                {[...Array(20)].map((_, i) => (
                  <div key={i} className="confetti-piece" style={{
                    backgroundColor: i % 2 === 0 ? currentTheme.primary : currentTheme.secondary,
                    animationDelay: `${i * 0.1}s`
                  }}></div>
                ))}
              </div>
            </div>
            <h2 className="success-title" style={{ color: currentTheme.primary }}>
              🎉 Recognition Sent Successfully!
            </h2>
            <p className="success-message">You've made someone's day brighter!</p>
            <div className="success-details">
              <div className="success-stat" style={{ background: selectedCategoryData?.gradient }}>
                <Star className="stat-icon" />
                <span>{points} points awarded</span>
              </div>
              <div className="success-stat" style={{ background: currentTheme.secondary }}>
                <Users className="stat-icon" />
                <span>{selectedPeople.length} recipient{selectedPeople.length > 1 ? 's' : ''}</span>
              </div>
              <div className="success-stat" style={{ background: currentTheme.accent }}>
                <Gift className="stat-icon" />
                <span>{selectedCategoryData?.badge} {selectedCategoryData?.label}</span>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Enhanced Header with Theme Colors */}
            <div className="modal-header" style={{ background: `linear-gradient(135deg, ${currentTheme.primary}, ${currentTheme.secondary})` }}>
              <div className="header-content">
                <div className="header-icon-wrapper">
                  <Gift className="header-icon" />
                </div>
                <div className="header-text">
                  <h2 className="modal-title">Give Recognition</h2>
                  <p className="modal-subtitle">Celebrate amazing work and spread positivity</p>
                </div>
              </div>
              <button onClick={onClose} className="close-button">
                <X className="close-icon" />
              </button>
            </div>

            {/* Progress Steps */}
            <div className="progress-steps">
              {[1, 2, 3, 4].map(step => (
                <div key={step} className={`step ${currentStep >= step ? 'active' : ''} ${currentStep > step ? 'completed' : ''}`}>
                  <div className="step-number" style={{
                    backgroundColor: currentStep >= step ? currentTheme.primary : '#e5e7eb',
                    color: currentStep >= step ? 'white' : '#6b7280'
                  }}>
                    {currentStep > step ? <CheckCircle size={16} /> : step}
                  </div>
                  <span className="step-label">
                    {step === 1 && 'Select People'}
                    {step === 2 && 'Choose Category'}
                    {step === 3 && 'Write Message'}
                    {step === 4 && 'Review & Send'}
                  </span>
                </div>
              ))}
            </div>

            {/* Theme Selector */}
            <div className="theme-selector">
              <h4>Choose Theme:</h4>
              <div className="theme-options">
                {Object.entries(themes).map(([themeName, theme]) => (
                  <button
                    key={themeName}
                    onClick={() => handleThemeChange(themeName)}
                    className={`theme-option ${selectedTheme === themeName ? 'selected' : ''}`}
                    style={{
                      background: `linear-gradient(135deg, ${theme.primary}, ${theme.secondary})`,
                      border: selectedTheme === themeName ? `2px solid ${theme.primary}` : '2px solid transparent'
                    }}
                    title={themeName.charAt(0).toUpperCase() + themeName.slice(1)}
                  >
                    <div className="theme-preview" style={{ backgroundColor: theme.accent }}></div>
                  </button>
                ))}
              </div>
            </div>

            <form onSubmit={handleSubmit} className="modal-form">
              {/* Step 1: Recipients */}
              {currentStep === 1 && (
                <div className="form-step">
                  <div className="step-header">
                    <h3 style={{ color: currentTheme.primary }}>👥 Select Recipients</h3>
                    <p>Choose who you want to recognize</p>
                  </div>

                  {/* Department Filter */}
                  <div className="department-filter">
                    <label>Filter by Department:</label>
                    <select
                      value={selectedDepartment}
                      onChange={(e) => setSelectedDepartment(e.target.value)}
                      className="department-select"
                    >
                      <option value="all">All Departments</option>
                      {[...new Set(colleagues.map(c => c.department))].map(dept => (
                        <option key={dept} value={dept}>{dept}</option>
                      ))}
                    </select>
                  </div>

                  <div className="search-container">
                    <Search className="search-icon" />
                    <input
                      type="text"
                      placeholder="Search colleagues by name, department, or role..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="search-input"
                    />
                  </div>

                  <div className="recipients-grid">
                    {filteredColleagues.map(colleague => (
                      <div
                        key={colleague.id}
                        onClick={() => handlePersonToggle(colleague)}
                        className={`recipient-card ${selectedPeople.find(p => p.id === colleague.id) ? 'selected' : ''}`}
                        style={{
                          borderColor: selectedPeople.find(p => p.id === colleague.id) ? currentTheme.primary : '#e5e7eb',
                          background: selectedPeople.find(p => p.id === colleague.id) ? `${currentTheme.primary}10` : 'white'
                        }}
                      >
                        <div className="recipient-avatar" style={{ backgroundColor: colleague.color }}>
                          {colleague.avatar}
                        </div>
                        <div className="recipient-info">
                          <span className="recipient-name">{colleague.name}</span>
                          <span className="recipient-role">{colleague.role}</span>
                          <span className="recipient-department">{colleague.department}</span>
                          <div className="recipient-status">
                            <div className={`status-indicator ${colleague.status}`}></div>
                            <span>{colleague.status}</span>
                            <span className="points">{colleague.points} pts</span>
                          </div>
                        </div>
                        {selectedPeople.find(p => p.id === colleague.id) && (
                          <CheckCircle className="selected-icon" style={{ color: currentTheme.primary }} />
                        )}
                      </div>
                    ))}
                  </div>

                  {selectedPeople.length > 0 && (
                    <div className="selected-summary">
                      <h4>Selected Recipients ({selectedPeople.length}):</h4>
                      <div className="selected-tags">
                        {selectedPeople.map(person => (
                          <span key={person.id} className="selected-tag" style={{ backgroundColor: currentTheme.primary }}>
                            {person.name}
                            <button
                              type="button"
                              onClick={() => handlePersonToggle(person)}
                              className="remove-tag"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Step 2: Category */}
              {currentStep === 2 && (
                <div className="form-step">
                  <div className="step-header">
                    <h3 style={{ color: currentTheme.primary }}>🏆 Choose Recognition Category</h3>
                    <p>What are you recognizing them for?</p>
                  </div>

                  <div className="categories-grid">
                    {categories.map(category => {
                      const IconComponent = category.icon
                      const isSelected = selectedCategory === category.id
                      return (
                        <div
                          key={category.id}
                          onClick={() => handleCategorySelect(category.id)}
                          className={`category-card ${isSelected ? 'selected' : ''}`}
                          style={{
                            background: isSelected ? category.gradient : 'white',
                            borderColor: isSelected ? category.color : '#e5e7eb',
                            color: isSelected ? 'white' : '#374151'
                          }}
                        >
                          <div className="category-header">
                            <IconComponent className="category-icon" />
                            <span className="category-badge">{category.badge}</span>
                          </div>
                          <h4 className="category-title">{category.label}</h4>
                          <p className="category-description">{category.description}</p>
                          <div className="category-points" style={{
                            backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : category.color + '20',
                            color: isSelected ? 'white' : category.color
                          }}>
                            <Star className="points-icon" size={16} />
                            <span>{category.points} points</span>
                          </div>
                          {isSelected && (
                            <CheckCircle className="selected-check" />
                          )}
                        </div>
                      )
                    })}
                  </div>

                  {selectedCategory && (
                    <div className="category-preview" style={{
                      background: selectedCategoryData?.gradient,
                      color: 'white'
                    }}>
                      <div className="preview-content">
                        <span className="preview-badge">{selectedCategoryData?.badge}</span>
                        <h4>{selectedCategoryData?.label}</h4>
                        <p>{selectedCategoryData?.description}</p>
                        <div className="preview-points">
                          <Star size={20} />
                          <span>{selectedCategoryData?.points} points will be awarded</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Step 3: Message */}
              {currentStep === 3 && (
                <div className="form-step">
                  <div className="step-header">
                    <h3 style={{ color: currentTheme.primary }}>✍️ Write Your Message</h3>
                    <p>Express your appreciation with a heartfelt message</p>
                  </div>

                  {/* Quick Message Templates */}
                  <div className="quick-messages">
                    <h4>Quick Templates:</h4>
                    <div className="message-templates">
                      {quickMessages.filter(msg => !selectedCategory || msg.category === selectedCategory).map((template, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleQuickMessage(template.text, template.tags)}
                          className="template-button"
                          style={{ borderColor: currentTheme.primary }}
                        >
                          {template.text}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="message-input-container">
                    <textarea
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      placeholder="Write your personalized message of appreciation..."
                      className="message-textarea"
                      rows={6}
                      style={{ borderColor: currentTheme.primary }}
                    />
                    <div className="message-tools">
                      <button
                        type="button"
                        className="tool-button"
                        onClick={() => setShowEmojiPicker(true)}
                        style={{ color: currentTheme.primary }}
                      >
                        <Smile size={20} />
                        Add Emoji
                      </button>
                      <label className="tool-button" style={{ color: currentTheme.secondary }}>
                        <Image size={20} />
                        Add Image
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                          style={{ display: 'none' }}
                        />
                      </label>
                    </div>
                  </div>

                  {attachments.length > 0 && (
                    <div className="attachments-preview">
                      <h4>Attachments:</h4>
                      <div className="attachments-list">
                        {attachments.map((file, index) => (
                          <div key={index} className="attachment-item" style={{ backgroundColor: currentTheme.accent + '20' }}>
                            <Image size={16} />
                            <span>{file.name}</span>
                            <button onClick={() => setAttachments(prev => prev.filter((_, i) => i !== index))}>×</button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Visibility Settings */}
                  <div className="visibility-section">
                    <h4>Visibility Settings:</h4>
                    <div className="visibility-options">
                      {[
                        { value: 'public', label: 'Public', desc: 'Visible to everyone', icon: '🌍' },
                        { value: 'team', label: 'Team Only', desc: 'Visible to team members', icon: '👥' },
                        { value: 'private', label: 'Private', desc: 'Only recipient can see', icon: '🔒' }
                      ].map(option => (
                        <label key={option.value} className="visibility-option">
                          <input
                            type="radio"
                            name="visibility"
                            value={option.value}
                            checked={visibility === option.value}
                            onChange={(e) => setVisibility(e.target.value)}
                          />
                          <div className="option-content" style={{
                            borderColor: visibility === option.value ? currentTheme.primary : '#e5e7eb',
                            backgroundColor: visibility === option.value ? currentTheme.primary + '10' : 'white'
                          }}>
                            <span className="option-icon">{option.icon}</span>
                            <div className="option-text">
                              <span className="option-label">{option.label}</span>
                              <span className="option-desc">{option.desc}</span>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Step 4: Review & Send */}
              {currentStep === 4 && (
                <div className="form-step">
                  <div className="step-header">
                    <h3 style={{ color: currentTheme.primary }}>🎉 Review & Send</h3>
                    <p>Review your recognition before sending</p>
                  </div>

                  <div className="review-card" style={{ borderColor: currentTheme.primary }}>
                    <div className="review-header" style={{ background: selectedCategoryData?.gradient }}>
                      <span className="review-badge">{selectedCategoryData?.badge}</span>
                      <h4>{selectedCategoryData?.label}</h4>
                      <div className="review-points">
                        <Star size={20} />
                        <span>{points} points</span>
                      </div>
                    </div>

                    <div className="review-content">
                      <div className="review-section">
                        <h5>Recipients ({selectedPeople.length}):</h5>
                        <div className="review-recipients">
                          {selectedPeople.map(person => (
                            <div key={person.id} className="review-recipient">
                              <div className="recipient-avatar" style={{ backgroundColor: person.color }}>
                                {person.avatar}
                              </div>
                              <div>
                                <span className="name">{person.name}</span>
                                <span className="dept">{person.department}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="review-section">
                        <h5>Message:</h5>
                        <div className="review-message">{message}</div>
                      </div>

                      <div className="review-section">
                        <h5>Visibility:</h5>
                        <span className="review-visibility">{visibility.charAt(0).toUpperCase() + visibility.slice(1)}</span>
                      </div>

                      {attachments.length > 0 && (
                        <div className="review-section">
                          <h5>Attachments:</h5>
                          <div className="review-attachments">
                            {attachments.map((file, index) => (
                              <span key={index} className="attachment-tag">📎 {file.name}</span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="form-navigation">
                <div className="nav-buttons">
                  {currentStep > 1 && (
                    <button
                      type="button"
                      onClick={() => handleStepNavigation('prev')}
                      className="nav-button prev-button"
                      style={{ borderColor: currentTheme.secondary, color: currentTheme.secondary }}
                    >
                      ← Previous
                    </button>
                  )}

                  <button
                    type="button"
                    onClick={onClose}
                    className="cancel-button"
                  >
                    Cancel
                  </button>

                  {currentStep < 4 ? (
                    <button
                      type="button"
                      onClick={() => handleStepNavigation('next')}
                      className="nav-button next-button"
                      style={{
                        backgroundColor: currentTheme.primary,
                        borderColor: currentTheme.primary
                      }}
                      disabled={
                        (currentStep === 1 && selectedPeople.length === 0) ||
                        (currentStep === 2 && !selectedCategory) ||
                        (currentStep === 3 && !message.trim())
                      }
                    >
                      Next →
                    </button>
                  ) : (
                    <button
                      type="submit"
                      className="submit-button"
                      style={{
                        background: selectedCategoryData?.gradient,
                        borderColor: selectedCategoryData?.color
                      }}
                      disabled={isSubmitting || selectedPeople.length === 0 || !selectedCategory || !message.trim()}
                    >
                      {isSubmitting ? (
                        <>
                          <div className="loading-spinner"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send size={20} />
                          Send Recognition
                        </>
                      )}
                    </button>
                  )}
                </div>

                {/* Progress Indicator */}
                <div className="step-progress">
                  <div className="progress-bar">
                    <div
                      className="progress-fill"
                      style={{
                        width: `${(currentStep / 4) * 100}%`,
                        backgroundColor: currentTheme.primary
                      }}
                    ></div>
                  </div>
                  <span className="progress-text">
                    Step {currentStep} of 4
                  </span>
                </div>
              </div>
            </form>
          </>
        )}
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelect={handleEmojiSelect}
          onClose={() => setShowEmojiPicker(false)}
        />
      )}
    </div>
  )
}

export default GiveRecognitionModal
