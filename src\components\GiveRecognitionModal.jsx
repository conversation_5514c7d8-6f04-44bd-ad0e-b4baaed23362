import { useState, useEffect } from 'react'
import {
  <PERSON>, <PERSON>, <PERSON>, Send, Spark<PERSON>, <PERSON>,
  Users, Zap, Crown, Gift, Coffee, Rocket,
  Smile, Camera, Music, Gamepad2, Palette
} from 'lucide-react'
import './GiveRecognitionModal.css'

const GiveRecognitionModal = ({ isOpen, onClose }) => {
  const [selectedPerson, setSelectedPerson] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [message, setMessage] = useState('')
  const [points, setPoints] = useState(25)
  const [showSuccess, setShowSuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [colorTheme, setColorTheme] = useState('rainbow')
  const [quickMode, setQuickMode] = useState(false)

  // Simple team data
  const team = [
    { id: 1, name: '<PERSON>', emoji: '👩‍💻', color: '#FF6B9D' },
    { id: 2, name: '<PERSON>', emoji: '👨‍🎨', color: '#4ECDC4' },
    { id: 3, name: '<PERSON>', emoji: '👩‍💼', color: '#45B7<PERSON>1' },
    { id: 4, name: '<PERSON> <PERSON>', emoji: '👨‍🔬', color: '#96CEB4' },
    { id: 5, name: 'Lisa Wang', emoji: '👩‍🚀', color: '#FFEAA7' },
    { id: 6, name: 'Alex Morgan', emoji: '👨‍🎯', color: '#DDA0DD' },
  ]

  // Simple colorful categories
  const categories = [
    { id: 'star', label: '⭐ Star Player', icon: Star, color: '#FFD700', bg: 'linear-gradient(135deg, #FFD700, #FFA500)' },
    { id: 'team', label: '🤝 Team Spirit', icon: Users, color: '#FF6B9D', bg: 'linear-gradient(135deg, #FF6B9D, #C44569)' },
    { id: 'rocket', label: '🚀 Innovation', icon: Rocket, color: '#4ECDC4', bg: 'linear-gradient(135deg, #4ECDC4, #44A08D)' },
    { id: 'crown', label: '👑 Leadership', icon: Crown, color: '#A8E6CF', bg: 'linear-gradient(135deg, #A8E6CF, #88D8A3)' },
    { id: 'zap', label: '⚡ Quick Wins', icon: Zap, color: '#FFB6C1', bg: 'linear-gradient(135deg, #FFB6C1, #FFA0B4)' },
    { id: 'heart', label: '💖 Helpful', icon: Heart, color: '#DDA0DD', bg: 'linear-gradient(135deg, #DDA0DD, #DA70D6)' },
  ]

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        recipients: [],
        category: '',
        message: '',
        visibility: 'public',
        attachments: [],
        points: 10
      })
      setSearchTerm('')
      setFormErrors({})
    }
  }, [isOpen])

  const validateForm = () => {
    const errors = {}
    if (formData.recipients.length === 0) {
      errors.recipients = 'Please select at least one recipient'
    }
    if (!formData.category) {
      errors.category = 'Please select a category'
    }
    if (!formData.message.trim()) {
      errors.message = 'Please enter a message'
    } else if (formData.message.trim().length < 10) {
      errors.message = 'Message must be at least 10 characters'
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleRecipientToggle = (colleague) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.find(r => r.id === colleague.id)
        ? prev.recipients.filter(r => r.id !== colleague.id)
        : [...prev.recipients, colleague]
    }))
    if (formErrors.recipients) {
      setFormErrors(prev => ({ ...prev, recipients: null }))
    }
  }

  const handleEmojiSelect = (emoji) => {
    setFormData(prev => ({
      ...prev,
      message: prev.message + emoji
    }))
    if (formErrors.message) {
      setFormErrors(prev => ({ ...prev, message: null }))
    }
  }

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files)
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }))
  }

  const handleRemoveAttachment = (index) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false)
      setShowSuccess(true)
      setTimeout(() => {
        setShowSuccess(false)
        onClose()
      }, 2000)
    }, 800)
  }

  const handleCategorySelect = (categoryValue) => {
    setFormData(prev => ({ ...prev, category: categoryValue }))
    if (formErrors.category) {
      setFormErrors(prev => ({ ...prev, category: null }))
    }
  }

  const handleMessageChange = (e) => {
    setFormData(prev => ({ ...prev, message: e.target.value }))
    if (formErrors.message && e.target.value.trim().length >= 10) {
      setFormErrors(prev => ({ ...prev, message: null }))
    }
  }

  // Initialize data on component mount
  useEffect(() => {
    if (isOpen) {
      const generatedColleagues = generateColleagues()
      setColleagues(generatedColleagues)

      const uniqueDepartments = [...new Set(generatedColleagues.map(c => c.department))]
      setDepartments(uniqueDepartments)
    }
  }, [isOpen])

  // New functionality handlers
  const handleThemeChange = (theme) => {
    setSelectedTheme(theme)
  }

  const handleRecognitionTypeChange = (type) => {
    setRecognitionType(type)
    if (type === 'team') {
      // Auto-select team members
      const teamMembers = colleagues.filter(c => c.department === departments[0])
      setFormData(prev => ({ ...prev, recipients: teamMembers.slice(0, 3) }))
    }
  }

  const handleScheduleToggle = () => {
    setIsScheduled(!isScheduled)
    if (!isScheduled) {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      setScheduledDate(tomorrow.toISOString().split('T')[0])
    } else {
      setScheduledDate('')
    }
  }

  const handleQuickMessage = (template) => {
    setFormData(prev => ({ ...prev, message: template }))
  }

  const filteredColleagues = colleagues.filter(colleague =>
    colleague.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    colleague.department.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (!isOpen) return null

  const selectedCategoryData = categories.find(cat => cat.id === selectedCategory)
  const selectedPersonData = team.find(person => person.id === parseInt(selectedPerson))

  return (
    <div className="modal-overlay">
      <div className="modal-container recognition-modal">
        {showSuccess ? (
          <div className="success-message">
            <div className="success-animation">
              <div className="success-circle">
                <CheckCircle className="success-check" />
              </div>
              <div className="floating-sparkles">
                <Sparkles className="sparkle-1" />
                <Sparkles className="sparkle-2" />
                <Sparkles className="sparkle-3" />
              </div>
            </div>
            <h3 className="success-title">🎉 Recognition Sent Successfully!</h3>
            <p className="success-subtitle">You just made someone's day brighter and more meaningful.</p>
            <div className="success-details">
              <div className="points-awarded">
                <Star className="points-icon" />
                <span>{formData.points} points awarded</span>
              </div>
              <div className="recipients-count">
                <Users className="recipients-icon" />
                <span>{formData.recipients.length} recipient{formData.recipients.length > 1 ? 's' : ''}</span>
              </div>
              <div className="category-badge" style={{ background: selectedCategory?.gradient }}>
                {selectedCategory && <selectedCategory.icon className="category-badge-icon" />}
                <span>{selectedCategory?.label}</span>
              </div>
            </div>
            <div className="success-actions">
              <button
                onClick={() => {
                  setShowSuccess(false)
                  onClose()
                }}
                className="success-close-btn"
                style={{ background: selectedCategory?.gradient }}
              >
                <Send className="send-icon" />
                Done
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="modal-header recognition-header">
              <div className="header-content">
                <div className="header-title">
                  <ThumbsUp className="header-icon" />
                  <div>
                    <h2>Give Recognition</h2>
                    <p className="header-subtitle">Appreciate your colleagues' amazing work</p>
                  </div>
                </div>
                <div className="header-controls">
                  {/* Theme Selector */}
                  <div className="theme-selector">
                    <label className="control-label">Theme:</label>
                    <select
                      value={selectedTheme}
                      onChange={(e) => handleThemeChange(e.target.value)}
                      className="theme-select"
                    >
                      <option value="default">🎨 Default</option>
                      <option value="sunset">🌅 Sunset</option>
                      <option value="ocean">🌊 Ocean</option>
                      <option value="forest">🌲 Forest</option>
                    </select>
                  </div>

                  {/* Recognition Type */}
                  <div className="recognition-type-selector">
                    <label className="control-label">Type:</label>
                    <div className="type-buttons">
                      <button
                        type="button"
                        className={`type-btn ${recognitionType === 'individual' ? 'active' : ''}`}
                        onClick={() => handleRecognitionTypeChange('individual')}
                      >
                        <Users className="type-icon" />
                        Individual
                      </button>
                      <button
                        type="button"
                        className={`type-btn ${recognitionType === 'team' ? 'active' : ''}`}
                        onClick={() => handleRecognitionTypeChange('team')}
                      >
                        <Users className="type-icon" />
                        Team
                      </button>
                    </div>
                  </div>

                  {/* Schedule Toggle */}
                  <div className="schedule-toggle">
                    <label className="control-label">
                      <input
                        type="checkbox"
                        checked={isScheduled}
                        onChange={handleScheduleToggle}
                        className="schedule-checkbox"
                      />
                      <Calendar className="schedule-icon" />
                      Schedule
                    </label>
                    {isScheduled && (
                      <input
                        type="date"
                        value={scheduledDate}
                        onChange={(e) => setScheduledDate(e.target.value)}
                        className="schedule-date"
                        min={new Date().toISOString().split('T')[0]}
                      />
                    )}
                  </div>
                </div>

                <div className="progress-indicator">
                  <div className="progress-steps">
                    <div className={`step ${formData.recipients.length > 0 ? 'completed' : 'active'}`}>1</div>
                    <div className={`step ${formData.category ? 'completed' : formData.recipients.length > 0 ? 'active' : ''}`}>2</div>
                    <div className={`step ${formData.message.trim().length >= 10 ? 'completed' : formData.category ? 'active' : ''}`}>3</div>
                  </div>
                </div>
              </div>
              <button onClick={onClose} className="close-button">
                <X className="close-icon" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="modal-form">
              {/* Recipients */}
              <div className="form-group">
                <label className="form-label">
                  Recipient(s) <span className="required">*</span>
                </label>
                <div className="search-container">
                  <Search className="search-icon" />
                  <input
                    type="text"
                    placeholder="Search colleagues..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={`search-input ${formErrors.recipients ? 'input-error' : ''}`}
                  />
                </div>
                {formErrors.recipients && (
                  <div className="error-message">{formErrors.recipients}</div>
                )}
                <div className="recipients-list">
                  {filteredColleagues.map(colleague => (
                    <div
                      key={colleague.id}
                      onClick={() => handleRecipientToggle(colleague)}
                      className={`recipient-item ${formData.recipients.find(r => r.id === colleague.id) ? 'selected' : ''}`}
                    >
                      <div className="recipient-avatar" style={{ backgroundColor: colleague.color }}>
                        {colleague.avatar}
                        <div className={`status-indicator ${colleague.status}`}></div>
                      </div>
                      <div className="recipient-info">
                        <div className="recipient-main">
                          <span className="recipient-name">{colleague.name}</span>
                          <span className="recipient-role">{colleague.role}</span>
                        </div>
                        <div className="recipient-meta">
                          <span className="recipient-department">{colleague.department}</span>
                          <span className="recipient-points">{colleague.points} pts</span>
                        </div>
                        <span className="recipient-activity">Last active: {colleague.lastActive}</span>
                      </div>
                      {formData.recipients.find(r => r.id === colleague.id) && (
                        <div className="selected-check">
                          <CheckCircle className="check-icon" />
                        </div>
                      )}
                    </div>
                  ))}
                  {filteredColleagues.length === 0 && (
                    <div className="no-results">
                      <Users className="no-results-icon" />
                      <span>No colleagues found</span>
                    </div>
                  )}
                </div>
                {formData.recipients.length > 0 && (
                  <div className="selected-recipients">
                    <span className="selected-label">Selected:</span>
                    {formData.recipients.map(recipient => (
                      <span key={recipient.id} className="selected-tag">
                        {recipient.name}
                        <button
                          type="button"
                          onClick={() => handleRecipientToggle(recipient)}
                          className="remove-tag"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Category */}
              <div className="form-group">
                <label className="form-label">
                  Category / Reason <span className="required">*</span>
                </label>
                {formErrors.category && (
                  <div className="error-message">{formErrors.category}</div>
                )}
                <div className="category-grid">
                  {categories.map(category => (
                    <button
                      key={category.value}
                      type="button"
                      onClick={() => handleCategorySelect(category.value)}
                      className={`category-item ${formData.category === category.value ? 'selected' : ''}`}
                      style={formData.category === category.value ?
                        {
                          borderColor: category.color,
                          background: category.gradient,
                          color: 'white',
                          transform: 'translateY(-4px)',
                          boxShadow: `0 8px 25px ${category.color}40`
                        } : {}}
                    >
                      <category.icon
                        className="category-icon"
                        style={{
                          color: formData.category === category.value ? 'white' : category.color
                        }}
                      />
                      <span>{category.label}</span>
                      {formData.category === category.value && (
                        <div className="selected-indicator">
                          <CheckCircle className="check-icon" />
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Points */}
              {selectedCategory && (
                <div className="form-group points-section" style={{ background: `${selectedCategory.color}10` }}>
                  <label className="form-label" style={{ color: selectedCategory.color }}>
                    <Trophy className="points-label-icon" />
                    Recognition Points
                  </label>
                  <div className="points-slider-container">
                    <input
                      type="range"
                      min="5"
                      max="50"
                      step="5"
                      value={formData.points}
                      onChange={(e) => {
                        setFormData(prev => ({ ...prev, points: parseInt(e.target.value) }))
                        setAnimatePoints(true)
                        setTimeout(() => setAnimatePoints(false), 300)
                      }}
                      className="points-slider"
                      style={{
                        '--slider-color': selectedCategory.color
                      }}
                    />
                    <div
                      className={`points-value ${animatePoints ? 'animate' : ''}`}
                      style={{ color: selectedCategory.color }}
                    >
                      <Star className="points-star-icon" />
                      <span>{formData.points} points</span>
                      <div className="points-description">
                        {formData.points <= 15 ? 'Nice recognition!' :
                         formData.points <= 30 ? 'Great appreciation!' :
                         'Outstanding recognition!'}
                      </div>
                    </div>
                  </div>
                  <div className="points-scale">
                    <span className="scale-label">Good</span>
                    <span className="scale-label">Great</span>
                    <span className="scale-label">Outstanding</span>
                  </div>
                </div>
              )}

              {/* Message */}
              <div className="form-group">
                <label className="form-label">
                  Message <span className="required">*</span>
                </label>

                {/* Quick Message Templates */}
                <div className="quick-templates">
                  <label className="templates-label">Quick Templates:</label>
                  <div className="template-buttons">
                    <button
                      type="button"
                      className="template-btn"
                      onClick={() => handleQuickMessage("Thank you for your outstanding work on this project! Your dedication and attention to detail made all the difference. 🌟")}
                    >
                      <Sparkles className="template-icon" />
                      Outstanding Work
                    </button>
                    <button
                      type="button"
                      className="template-btn"
                      onClick={() => handleQuickMessage("Your teamwork and collaboration skills are exceptional! You always bring out the best in everyone around you. 🤝")}
                    >
                      <Users className="template-icon" />
                      Great Teamwork
                    </button>
                    <button
                      type="button"
                      className="template-btn"
                      onClick={() => handleQuickMessage("Your innovative thinking and creative solutions continue to amaze me! Keep up the fantastic work! 💡")}
                    >
                      <Lightbulb className="template-icon" />
                      Innovation
                    </button>
                    <button
                      type="button"
                      className="template-btn"
                      onClick={() => handleQuickMessage("Your leadership and guidance have been invaluable to our team's success. Thank you for being an inspiration! 👑")}
                    >
                      <Crown className="template-icon" />
                      Leadership
                    </button>
                  </div>
                </div>

                <textarea
                  value={formData.message}
                  onChange={handleMessageChange}
                  placeholder="Write your message of appreciation…"
                  className={`message-textarea ${formErrors.message ? 'input-error' : ''}`}
                  rows={4}
                />
                {formErrors.message && (
                  <div className="error-message">{formErrors.message}</div>
                )}
                <div className="message-footer">
                  <div className="character-count">
                    {formData.message.length} characters
                  </div>
                  <div className="message-tips">
                    💡 Tip: Be specific about what they did well!
                  </div>
                </div>
              </div>

              {/* Optional Features */}
              <div className="form-group">
                <label className="form-label">Add Fun Elements (Optional)</label>
                <div className="optional-features">
                  <button
                    type="button"
                    className="feature-button"
                    onClick={() => setShowEmojiPicker(true)}
                  >
                    <Smile className="feature-icon" />
                    Add Emoji/GIF
                  </button>
                  <label className="feature-button">
                    <Image className="feature-icon" />
                    Add Image
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                    />
                  </label>
                  <label className="feature-button">
                    <Upload className="feature-icon" />
                    Attach File
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                    />
                  </label>
                </div>
                {formData.attachments.length > 0 && (
                  <div className="attachments-list">
                    {formData.attachments.map((file, index) => (
                      <div key={index} className="attachment-item">
                        <span>{file.name}</span>
                        <button 
                          type="button" 
                          onClick={() => handleRemoveAttachment(index)}
                          className="remove-attachment"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Visibility */}
              <div className="form-group">
                <label className="form-label">Visibility</label>
                <div className="visibility-options">
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="public"
                      checked={formData.visibility === 'public'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Public (visible to all)</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="team"
                      checked={formData.visibility === 'team'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Team Only</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="visibility"
                      value="private"
                      checked={formData.visibility === 'private'}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                    />
                    <span className="radio-label">Private (visible to recipient only)</span>
                  </label>
                </div>
              </div>

              {/* Submit Button */}
              <div className="form-actions">
                <button type="button" onClick={onClose} className="cancel-button">
                  <X className="cancel-icon" />
                  Cancel
                </button>

                {/* Preview Button */}
                <button
                  type="button"
                  className="preview-button"
                  onClick={() => setShowPreview(!showPreview)}
                  disabled={!formData.recipients.length || !formData.category || !formData.message.trim()}
                >
                  <Eye className="preview-icon" />
                  {showPreview ? 'Hide Preview' : 'Preview'}
                </button>

                <button
                  type="submit"
                  className={`submit-button ${isSubmitting ? 'loading' : ''}`}
                  disabled={isSubmitting}
                  style={selectedCategory ? { background: selectedCategory.gradient } : {}}
                >
                  {isSubmitting ? (
                    <>
                      <div className="loading-spinner"></div>
                      {isScheduled ? 'Scheduling...' : 'Sending Recognition...'}
                    </>
                  ) : (
                    <>
                      {isScheduled ? (
                        <>
                          <Calendar className="submit-icon" />
                          Schedule for {new Date(scheduledDate).toLocaleDateString()}
                        </>
                      ) : (
                        <>
                          <Send className="submit-icon" />
                          Send Recognition
                        </>
                      )}
                      <Sparkles className="sparkle-submit" />
                    </>
                  )}
                </button>
              </div>

              {/* Recognition Preview */}
              {showPreview && formData.recipients.length > 0 && formData.category && formData.message.trim() && (
                <div className="recognition-preview" style={{ borderColor: selectedCategory?.color }}>
                  <div className="preview-header">
                    <h4>Recognition Preview</h4>
                    <div className="preview-theme" style={{ background: selectedCategory?.gradient }}>
                      {selectedTheme} theme
                    </div>
                  </div>
                  <div className="preview-content">
                    <div className="preview-category" style={{ background: selectedCategory?.gradient }}>
                      {selectedCategory && <selectedCategory.icon className="preview-category-icon" />}
                      {selectedCategory?.label}
                    </div>
                    <div className="preview-recipients">
                      <strong>To:</strong> {formData.recipients.map(r => r.name).join(', ')}
                    </div>
                    <div className="preview-message">
                      {formData.message}
                    </div>
                    <div className="preview-footer">
                      <div className="preview-points">
                        <Star className="preview-star" />
                        {formData.points} points
                      </div>
                      <div className="preview-visibility">
                        Visibility: {formData.visibility}
                      </div>
                      {isScheduled && (
                        <div className="preview-schedule">
                          <Calendar className="preview-calendar" />
                          Scheduled for: {new Date(scheduledDate).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </form>
          </>
        )}
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelect={handleEmojiSelect}
          onClose={() => setShowEmojiPicker(false)}
        />
      )}
    </div>
  )
}

export default GiveRecognitionModal