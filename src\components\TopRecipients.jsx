import { useState, useEffect } from 'react'
import {
  Trophy, Crown, Star, Medal, Award, TrendingUp, Users,
  Zap, Target, Heart, Gift, Sparkles, ChevronRight,
  Filter, Calendar, BarChart3, Eye, Share2, Download
} from 'lucide-react'
import RecipientDetailsModal from './RecipientDetailsModal'
import './TopRecipients.css'

const TopRecipients = () => {
  const [timeFilter, setTimeFilter] = useState('month')
  const [showFilters, setShowFilters] = useState(false)
  const [animateRanks, setAnimateRanks] = useState(false)

  const allRecipients = {
    week: [
      {
        rank: 1,
        name: '<PERSON>',
        points: 450,
        badges: 6,
        avatar: 'AM',
        color: '#FF6B9D',
        gradient: 'linear-gradient(135deg, #FF6B9D, #C44569)',
        department: 'Engineering',
        recognitions: 12,
        trending: true,
        growth: '+15%',
        level: 'Expert',
        achievements: ['Innovation Leader', 'Team Player', 'Problem Solver'],
        recentBadges: ['🚀', '💡', '⭐'],
        joinDate: '2023-01-15',
        totalGiven: 89,
        streak: 7,
      },
      {
        rank: 2,
        name: '<PERSON>',
        points: 380,
        badges: 4,
        avatar: 'JW',
        color: '#4ECDC4',
        gradient: 'linear-gradient(135deg, #4ECDC4, #44A08D)',
        department: 'Design',
        recognitions: 9,
        trending: false,
        growth: '+8%',
        level: 'Advanced',
        achievements: ['Creative Genius', 'Mentor'],
        recentBadges: ['🎨', '🏆'],
        joinDate: '2023-02-20',
        totalGiven: 67,
        streak: 4,
      },
      {
        rank: 3,
        name: 'Lisa Chen',
        points: 320,
        badges: 5,
        avatar: 'LC',
        color: '#45B7D1',
        gradient: 'linear-gradient(135deg, #45B7D1, #2980B9)',
        department: 'Marketing',
        recognitions: 8,
        trending: true,
        growth: '+12%',
        level: 'Advanced',
        achievements: ['Communication Expert', 'Strategy Leader'],
        recentBadges: ['📈', '🎯', '💬'],
        joinDate: '2023-03-10',
        totalGiven: 54,
        streak: 3,
      },
      {
        rank: 4,
        name: 'David Kim',
        points: 285,
        badges: 3,
        avatar: 'DK',
        color: '#96CEB4',
        gradient: 'linear-gradient(135deg, #96CEB4, #6AB7AA)',
        department: 'Sales',
        recognitions: 7,
        trending: false,
        growth: '+5%',
        level: 'Intermediate',
        achievements: ['Customer Champion'],
        recentBadges: ['💼', '🤝'],
        joinDate: '2023-04-05',
        totalGiven: 42,
        streak: 2,
      },
      {
        rank: 5,
        name: 'Sarah Johnson',
        points: 260,
        badges: 4,
        avatar: 'SJ',
        color: '#FFEAA7',
        gradient: 'linear-gradient(135deg, #FFEAA7, #FDCB6E)',
        department: 'HR',
        recognitions: 6,
        trending: true,
        growth: '+18%',
        level: 'Intermediate',
        achievements: ['People Person', 'Culture Builder'],
        recentBadges: ['👥', '🌟'],
        joinDate: '2023-05-12',
        totalGiven: 38,
        streak: 5,
      }
    ],
    month: [
      {
        rank: 1,
        name: 'Alex Morgan',
        points: 1250,
        badges: 15,
        avatar: 'AM',
        color: '#FF6B9D',
        gradient: 'linear-gradient(135deg, #FF6B9D, #C44569)',
        department: 'Engineering',
        recognitions: 28,
        trending: true,
        growth: '+22%',
        level: 'Expert',
        achievements: ['Innovation Leader', 'Team Player', 'Problem Solver', 'Mentor'],
        recentBadges: ['🚀', '💡', '⭐', '🏆', '🎯'],
        joinDate: '2023-01-15',
        totalGiven: 189,
        streak: 15,
      },
      {
        rank: 2,
        name: 'Lisa Chen',
        points: 980,
        badges: 12,
        avatar: 'LC',
        color: '#45B7D1',
        gradient: 'linear-gradient(135deg, #45B7D1, #2980B9)',
        department: 'Marketing',
        recognitions: 22,
        trending: true,
        growth: '+19%',
        level: 'Expert',
        achievements: ['Communication Expert', 'Strategy Leader', 'Growth Hacker'],
        recentBadges: ['📈', '🎯', '💬', '🚀'],
        joinDate: '2023-03-10',
        totalGiven: 134,
        streak: 12,
      },
      {
        rank: 3,
        name: 'James Wilson',
        points: 850,
        badges: 10,
        avatar: 'JW',
        color: '#4ECDC4',
        gradient: 'linear-gradient(135deg, #4ECDC4, #44A08D)',
        department: 'Design',
        recognitions: 18,
        trending: false,
        growth: '+8%',
        level: 'Advanced',
        achievements: ['Creative Genius', 'Mentor', 'Design Leader'],
        recentBadges: ['🎨', '🏆', '✨'],
        joinDate: '2023-02-20',
        totalGiven: 167,
        streak: 8,
      }
    ]
  }

  const topRecipients = allRecipients[timeFilter] || allRecipients.month

  const [selectedRecipient, setSelectedRecipient] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    setAnimateRanks(true)
    const timer = setTimeout(() => setAnimateRanks(false), 1000)
    return () => clearTimeout(timer)
  }, [timeFilter])

  const handleRecipientClick = (recipient) => {
    setSelectedRecipient(recipient)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedRecipient(null)
  }

  const handleTimeFilterChange = (filter) => {
    setTimeFilter(filter)
    setAnimateRanks(true)
  }

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1: return <Crown className="rank-icon gold" />
      case 2: return <Medal className="rank-icon silver" />
      case 3: return <Award className="rank-icon bronze" />
      default: return <Trophy className="rank-icon default" />
    }
  }

  const getRankColor = (rank) => {
    switch (rank) {
      case 1: return 'linear-gradient(135deg, #FFD700, #FFA500)'
      case 2: return 'linear-gradient(135deg, #C0C0C0, #A8A8A8)'
      case 3: return 'linear-gradient(135deg, #CD7F32, #B8860B)'
      default: return 'linear-gradient(135deg, #e5e7eb, #d1d5db)'
    }
  }

  return (
    <div className="top-recipients-card">
      {/* Enhanced Header with Filters */}
      <div className="top-recipients-header">
        <div className="header-content">
          <div className="header-title-section">
            <Sparkles className="header-icon" />
            <h2 className="top-recipients-title">Top Recognition Recipients</h2>
            <div className="header-badge">
              <Star className="badge-icon" />
              <span>{topRecipients.length}</span>
            </div>
          </div>
          <div className="header-actions">
            <button
              className="filter-btn"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="filter-icon" />
              <span>Filters</span>
            </button>
            <button className="export-btn">
              <Download className="export-icon" />
            </button>
          </div>
        </div>

        {/* Time Filter Tabs */}
        <div className="time-filters">
          {['week', 'month'].map((filter) => (
            <button
              key={filter}
              className={`time-filter-btn ${timeFilter === filter ? 'active' : ''}`}
              onClick={() => handleTimeFilterChange(filter)}
            >
              <Calendar className="time-filter-icon" />
              <span>{filter === 'week' ? 'This Week' : 'This Month'}</span>
              {filter === timeFilter && <div className="active-indicator" />}
            </button>
          ))}
        </div>
      </div>

      {/* Enhanced Recipients List */}
      <div className="top-recipients-content">
        {topRecipients.map((recipient, index) => (
          <div
            key={recipient.rank}
            className={`recipient-item clickable ${animateRanks ? 'animate-in' : ''}`}
            style={{
              animationDelay: `${index * 0.1}s`,
              '--recipient-color': recipient.color,
              '--recipient-gradient': recipient.gradient
            }}
            onClick={() => handleRecipientClick(recipient)}
          >
            {/* Enhanced Rank with Icons */}
            <div
              className="recipient-rank"
              style={{ background: getRankColor(recipient.rank) }}
            >
              {getRankIcon(recipient.rank)}
              <span className="recipient-rank-number">{recipient.rank}</span>
            </div>

            {/* Enhanced Avatar with Gradient */}
            <div
              className="recipient-avatar"
              style={{ background: recipient.gradient }}
            >
              <span className="recipient-avatar-text">{recipient.avatar}</span>
              <div className="avatar-ring"></div>
              {recipient.trending && (
                <div className="trending-indicator">
                  <TrendingUp className="trending-icon" />
                </div>
              )}
            </div>

            {/* Enhanced Info Section */}
            <div className="recipient-info">
              <div className="recipient-main-info">
                <h3 className="recipient-name">{recipient.name}</h3>
                <div className="recipient-level" style={{ color: recipient.color }}>
                  {recipient.level}
                </div>
              </div>
              <div className="recipient-department">{recipient.department}</div>
              <div className="recipient-stats">
                <div className="stat-item points">
                  <Star className="stat-icon" />
                  <span>{recipient.points.toLocaleString()} pts</span>
                </div>
                <div className="stat-item badges">
                  <Award className="stat-icon" />
                  <span>{recipient.badges} badges</span>
                </div>
                <div className="stat-item recognitions">
                  <Heart className="stat-icon" />
                  <span>{recipient.recognitions} received</span>
                </div>
              </div>
              <div className="recipient-badges">
                {recipient.recentBadges.slice(0, 3).map((badge, idx) => (
                  <span key={idx} className="recent-badge">{badge}</span>
                ))}
                {recipient.recentBadges.length > 3 && (
                  <span className="badge-count">+{recipient.recentBadges.length - 3}</span>
                )}
              </div>
            </div>

            {/* Growth Indicator */}
            <div className="recipient-growth">
              <div className="growth-value" style={{ color: recipient.color }}>
                {recipient.growth}
              </div>
              <div className="growth-label">Growth</div>
              {recipient.streak > 0 && (
                <div className="streak-indicator">
                  <Zap className="streak-icon" />
                  <span>{recipient.streak} day streak</span>
                </div>
              )}
            </div>

            {/* Action Arrow */}
            <div className="recipient-action">
              <ChevronRight className="action-arrow" />
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Stats Summary */}
      <div className="recipients-summary">
        <div className="summary-item">
          <Users className="summary-icon" />
          <div className="summary-content">
            <span className="summary-value">{topRecipients.length}</span>
            <span className="summary-label">Top Performers</span>
          </div>
        </div>
        <div className="summary-item">
          <Target className="summary-icon" />
          <div className="summary-content">
            <span className="summary-value">
              {topRecipients.reduce((sum, r) => sum + r.points, 0).toLocaleString()}
            </span>
            <span className="summary-label">Total Points</span>
          </div>
        </div>
        <div className="summary-item">
          <Gift className="summary-icon" />
          <div className="summary-content">
            <span className="summary-value">
              {topRecipients.reduce((sum, r) => sum + r.recognitions, 0)}
            </span>
            <span className="summary-label">Recognitions</span>
          </div>
        </div>
      </div>

      {/* Recipient Details Modal */}
      <RecipientDetailsModal
        recipient={selectedRecipient}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  )
}

export default TopRecipients
