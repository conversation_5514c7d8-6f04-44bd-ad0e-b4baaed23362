/* Enhanced Colorful TopRecipients Component Styles */
.top-recipients-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  position: relative;
}

.top-recipients-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* Enhanced Header */
.top-recipients-header {
  padding: 24px 24px 20px;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(78, 205, 196, 0.05));
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  width: 24px;
  height: 24px;
  color: #FF6B9D;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.top-recipients-title {
  font-size: 24px;
  font-weight: 800;
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.header-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #45B7D1, #2980B9);
  border-radius: 16px;
  color: white;
  font-size: 12px;
  font-weight: 700;
  box-shadow: 0 4px 12px rgba(69, 183, 209, 0.3);
}

.badge-icon {
  width: 14px;
  height: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-btn,
.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #f8fafc, #ffffff);
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-btn:hover,
.export-btn:hover {
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  border-color: #FF6B9D;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
}

.filter-icon,
.export-icon {
  width: 16px;
  height: 16px;
}

/* Time Filter Tabs */
.time-filters {
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.5);
  padding: 6px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.time-filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: transparent;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.time-filter-btn.active {
  background: linear-gradient(135deg, #45B7D1, #2980B9);
  color: white;
  box-shadow: 0 4px 12px rgba(69, 183, 209, 0.3);
}

.time-filter-icon {
  width: 16px;
  height: 16px;
}

.active-indicator {
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: #45B7D1;
  border-radius: 2px;
}

/* Enhanced Content */
.top-recipients-content {
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Enhanced Recipient Items */
.recipient-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.recipient-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--recipient-gradient);
  transition: width 0.3s ease;
}

.recipient-item.clickable {
  cursor: pointer;
}

.recipient-item.clickable:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--recipient-color);
}

.recipient-item.clickable:hover::before {
  width: 8px;
}

.recipient-item.animate-in {
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Rank */
.recipient-rank {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  color: white;
}

.rank-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 2px;
}

.rank-icon.gold {
  color: #FFD700;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.5));
}

.rank-icon.silver {
  color: #C0C0C0;
  filter: drop-shadow(0 0 8px rgba(192, 192, 192, 0.5));
}

.rank-icon.bronze {
  color: #CD7F32;
  filter: drop-shadow(0 0 8px rgba(205, 127, 50, 0.5));
}

.rank-icon.default {
  color: #9ca3af;
}

.recipient-rank-number {
  font-size: 12px;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Enhanced Avatar */
.recipient-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.recipient-avatar:hover {
  transform: scale(1.1);
}

.recipient-avatar-text {
  color: white;
  font-weight: 700;
  font-size: 18px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.avatar-ring {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.trending-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

.trending-icon {
  width: 12px;
  height: 12px;
  color: white;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Enhanced Info Section */
.recipient-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recipient-main-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.recipient-name {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.recipient-level {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid currentColor;
}

.recipient-department {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.recipient-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
}

.stat-icon {
  width: 14px;
  height: 14px;
  color: var(--recipient-color);
}

.recipient-badges {
  display: flex;
  align-items: center;
  gap: 6px;
}

.recent-badge {
  font-size: 16px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.recent-badge:hover {
  transform: scale(1.2);
}

.badge-count {
  font-size: 10px;
  font-weight: 700;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
}

/* Growth Indicator */
.recipient-growth {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}

.growth-value {
  font-size: 16px;
  font-weight: 800;
  text-align: center;
}

.growth-label {
  font-size: 10px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.streak-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-radius: 12px;
  color: white;
  font-size: 10px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.streak-icon {
  width: 10px;
  height: 10px;
}

/* Action Arrow */
.recipient-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.action-arrow {
  width: 16px;
  height: 16px;
  color: #6b7280;
  transition: transform 0.3s ease;
}

.recipient-item:hover .action-arrow {
  transform: translateX(4px);
  color: var(--recipient-color);
}

.recipient-item:hover .recipient-action {
  background: white;
  border-color: var(--recipient-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Enhanced Summary Section */
.recipients-summary {
  display: flex;
  gap: 16px;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(78, 205, 196, 0.05));
  border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.summary-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}

.summary-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.summary-icon {
  width: 24px;
  height: 24px;
  color: #FF6B9D;
}

.summary-content {
  display: flex;
  flex-direction: column;
}

.summary-value {
  font-size: 20px;
  font-weight: 800;
  color: #111827;
  line-height: 1;
}

.summary-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .top-recipients-card {
    border-radius: 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-title-section {
    justify-content: center;
  }

  .header-actions {
    justify-content: center;
  }

  .recipient-item {
    padding: 16px;
    gap: 12px;
  }

  .recipient-stats {
    gap: 12px;
  }

  .recipients-summary {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .top-recipients-header {
    padding: 20px 16px;
  }

  .top-recipients-content {
    padding: 16px;
    gap: 12px;
  }

  .top-recipients-title {
    font-size: 20px;
  }

  .time-filters {
    flex-direction: column;
    gap: 8px;
  }

  .recipient-item {
    padding: 16px;
    gap: 12px;
    flex-wrap: wrap;
  }

  .recipient-rank {
    width: 40px;
    height: 40px;
  }

  .rank-icon {
    width: 16px;
    height: 16px;
  }

  .recipient-avatar {
    width: 48px;
    height: 48px;
  }

  .recipient-avatar-text {
    font-size: 16px;
  }

  .recipient-name {
    font-size: 16px;
  }

  .recipient-stats {
    flex-direction: column;
    gap: 8px;
  }

  .recipient-growth {
    min-width: 60px;
  }

  .growth-value {
    font-size: 14px;
  }

  .recipients-summary {
    padding: 16px;
    gap: 12px;
  }

  .summary-item {
    padding: 12px;
  }

  .summary-value {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .top-recipients-header {
    padding: 16px 12px;
  }

  .top-recipients-content {
    padding: 12px;
    gap: 10px;
  }

  .header-title-section {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .top-recipients-title {
    font-size: 18px;
  }

  .recipient-item {
    padding: 12px;
    gap: 10px;
  }

  .recipient-rank {
    width: 36px;
    height: 36px;
  }

  .recipient-avatar {
    width: 40px;
    height: 40px;
  }

  .recipient-avatar-text {
    font-size: 14px;
  }

  .recipient-name {
    font-size: 14px;
  }

  .recipient-department {
    font-size: 12px;
  }

  .recipient-badges {
    flex-wrap: wrap;
  }

  .recent-badge {
    font-size: 14px;
  }

  .recipients-summary {
    padding: 12px;
    gap: 8px;
  }

  .summary-item {
    padding: 10px;
    gap: 8px;
  }

  .summary-icon {
    width: 20px;
    height: 20px;
  }

  .summary-value {
    font-size: 14px;
  }

  .summary-label {
    font-size: 10px;
  }
}

/* Loading and Animation States */
.recipient-item.loading {
  opacity: 0.6;
  pointer-events: none;
}

.recipient-item.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmerLoad 1.5s infinite;
}

@keyframes shimmerLoad {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .top-recipients-card {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: #374151;
  }

  .recipient-item {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border-color: #4b5563;
  }

  .recipient-name {
    color: #f9fafb;
  }

  .recipient-department,
  .stat-item {
    color: #d1d5db;
  }

  .summary-item {
    background: rgba(55, 65, 81, 0.8);
  }

  .summary-value {
    color: #f9fafb;
  }
}
