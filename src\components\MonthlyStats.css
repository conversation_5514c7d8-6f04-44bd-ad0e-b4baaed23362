/* Enhanced Colorful MonthlyStats Component Styles */
.monthly-stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  position: relative;
}

.monthly-stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FF6B9D, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* Enhanced Header */
.monthly-stats-header {
  padding: 24px 24px 20px;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(78, 205, 196, 0.05));
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.header-icon {
  width: 28px;
  height: 28px;
  color: #FF6B9D;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.title-info {
  flex: 1;
}

.monthly-stats-title {
  font-size: 24px;
  font-weight: 800;
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 4px 0;
}

.monthly-stats-subtitle {
  font-size: 14px;
  color: #6b7280;
  font-weight: 600;
  margin: 0;
}

.month-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #45B7D1, #2980B9);
  border-radius: 16px;
  color: white;
  font-size: 14px;
  font-weight: 700;
  box-shadow: 0 4px 12px rgba(69, 183, 209, 0.3);
}

.month-icon {
  width: 16px;
  height: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.month-toggle-btn,
.details-toggle-btn,
.export-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #f8fafc, #ffffff);
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.month-toggle-btn:hover,
.details-toggle-btn:hover,
.export-btn:hover {
  background: linear-gradient(135deg, #FF6B9D, #4ECDC4);
  border-color: #FF6B9D;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
}

.toggle-icon,
.details-icon,
.export-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.month-toggle-btn:hover .toggle-icon,
.details-toggle-btn:hover .details-icon,
.export-btn:hover .export-icon {
  transform: rotate(180deg);
}

/* Enhanced Content */
.monthly-stats-content {
  padding: 20px 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Enhanced Stat Items */
.stat-item {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--stat-gradient);
  transition: width 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--stat-color);
}

.stat-item:hover::before {
  width: 8px;
}

.stat-item.animate-in {
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Stat Header */
.stat-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.stat-icon-wrapper:hover {
  transform: scale(1.1);
}

.stat-icon {
  width: 24px;
  height: 24px;
  color: white;
}

.stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.stat-label {
  font-size: 16px;
  font-weight: 700;
  color: #111827;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
}

.trend-icon {
  width: 12px;
  height: 12px;
}

.trend-icon.up {
  color: #10b981;
}

.trend-icon.down {
  color: #ef4444;
}

.trend-icon.neutral {
  color: #6b7280;
}

.trend-value {
  font-weight: 700;
}

.stat-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  line-height: 1;
}

.stat-unit {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

/* Enhanced Progress Bar */
.stat-progress-container {
  position: relative;
  width: 100%;
  background: rgba(229, 231, 235, 0.5);
  border-radius: 12px;
  height: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.stat-progress-bar {
  height: 100%;
  border-radius: 12px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  font-weight: 600;
}

.progress-current {
  color: var(--stat-color);
}

.progress-max {
  color: #6b7280;
}

/* Enhanced Detail Sections */
.stat-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 16px;
  border: 1px solid rgba(229, 231, 235, 0.5);
  backdrop-filter: blur(10px);
}

.detail-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-title {
  font-size: 14px;
  font-weight: 700;
  color: #374151;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Week Comparison */
.week-comparison {
  display: flex;
  gap: 16px;
}

.week-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  text-align: center;
}

.week-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
}

.week-value {
  font-size: 20px;
  font-weight: 800;
}

/* Categories */
.categories-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
}

.category-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.category-name {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.category-count {
  font-size: 14px;
  font-weight: 700;
  color: #6b7280;
}

/* People Lists */
.people-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.person-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
}

.person-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 700;
}

.person-name {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.person-count {
  font-size: 14px;
  font-weight: 700;
  color: #6b7280;
}

/* Summary Section */
.stats-summary {
  padding: 24px;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(78, 205, 196, 0.05));
  border-radius: 20px;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.summary-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 800;
  color: #111827;
  margin: 0 0 20px 0;
}

.summary-icon {
  width: 24px;
  height: 24px;
  color: #FF6B9D;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.summary-card {
  padding: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.summary-card-title {
  font-size: 16px;
  font-weight: 700;
  color: #374151;
  margin: 0 0 16px 0;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .monthly-stats-header {
    padding: 20px 16px;
  }

  .monthly-stats-content {
    padding: 16px;
    gap: 16px;
  }

  .monthly-stats-title {
    font-size: 20px;
  }

  .stat-item {
    padding: 16px;
    gap: 12px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 14px;
  }
}
