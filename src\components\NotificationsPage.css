/* Notifications Page */
.notifications-page-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.notifications-page-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 900px;
  height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header */
.notifications-page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.back-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.header-title h1 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.header-title p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mark-all-read-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mark-all-read-button:hover {
  background-color: #2563eb;
}

.check-icon {
  width: 16px;
  height: 16px;
}

.close-page-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.close-page-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.close-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

/* Controls */
.notifications-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.search-container {
  position: relative;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
}

/* Content */
.notifications-page-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.notifications-page-list {
  display: flex;
  flex-direction: column;
}

.notification-page-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notification-page-item:hover {
  background-color: #f9fafb;
}

.notification-page-item:last-child {
  border-bottom: none;
}

.notification-page-item.unread {
  background-color: #eff6ff;
}

.notification-page-item.unread:hover {
  background-color: #dbeafe;
}

.notification-page-icon-container {
  flex-shrink: 0;
}

.notification-page-avatar {
  width: 40px;
  height: 40px;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.notification-page-icon-wrapper {
  width: 40px;
  height: 40px;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-page-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.notification-page-content-area {
  flex: 1;
  min-width: 0;
}

.notification-page-header-area {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
}

.notification-page-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  line-height: 1.4;
}

.notification-page-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
}

.notification-page-category {
  font-size: 12px;
  color: #3b82f6;
  background-color: #eff6ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.notification-page-time {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

.notification-page-message {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.notification-page-unread-indicator {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background-color: #3b82f6;
  border-radius: 50%;
}

/* No Notifications */
.no-notifications-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.no-notifications-icon {
  width: 64px;
  height: 64px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.no-notifications-found h3 {
  font-size: 18px;
  font-weight: 600;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.no-notifications-found p {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notifications-page-overlay {
    padding: 10px;
  }

  .notifications-page-container {
    height: 95vh;
  }

  .notifications-page-header {
    padding: 16px;
  }

  .header-left {
    gap: 12px;
  }

  .header-title h1 {
    font-size: 20px;
  }

  .notifications-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px 16px;
  }

  .notification-page-item {
    padding: 16px;
  }

  .notification-page-header-area {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .notification-page-meta {
    margin-left: 0;
  }
}
